﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="168px" height="79px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="650px" y="1160px" width="168px" height="79px" filterUnits="userSpaceOnUse" id="filter19">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.2 0  " in="shadowComposite" />
    </filter>
    <g id="widget20">
      <path d="M 660 1174  A 4 4 0 0 1 664 1170 L 804 1170  A 4 4 0 0 1 808 1174 L 808 1225  A 4 4 0 0 1 804 1229 L 664 1229  A 4 4 0 0 1 660 1225 L 660 1174  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" fill-opacity="0.8" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -650 -1160 )">
    <use xlink:href="#widget20" filter="url(#filter19)" />
    <use xlink:href="#widget20" />
  </g>
</svg>