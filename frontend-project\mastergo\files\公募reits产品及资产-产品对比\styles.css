﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:1912px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1572_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:1131px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1572 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:1131px;
  display:flex;
}
#u1572 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1572_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1574_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:884px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:10px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  -webkit-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
}
#u1574 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:200px;
  height:884px;
  display:flex;
}
#u1574 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1574_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1575_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u1575 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u1575 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1575_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1576_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:70px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  -webkit-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
}
#u1576 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:70px;
  display:flex;
}
#u1576 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1576_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1577 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1578_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1578 {
  border-width:0px;
  position:absolute;
  left:1840px;
  top:20px;
  width:30px;
  height:30px;
  display:flex;
}
#u1578 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1578_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1579_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:4px;
}
#u1579 {
  border-width:0px;
  position:absolute;
  left:1875px;
  top:33px;
  width:7px;
  height:4px;
  display:flex;
  color:#555555;
}
#u1579 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1579_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1580 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1581_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1581 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:26px;
  width:73px;
  height:19px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1581 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1581_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1582_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1582 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:20px;
  width:30px;
  height:30px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u1582 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1582_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1583 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1584_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:153px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:0px 0px 10px rgba(85, 85, 85, 0.34901960784313724);
  -webkit-box-shadow:0px 0px 10px rgba(85, 85, 85, 0.34901960784313724);
  box-shadow:0px 0px 10px rgba(85, 85, 85, 0.34901960784313724);
}
#u1584 {
  border-width:0px;
  position:absolute;
  left:1762px;
  top:59px;
  width:150px;
  height:153px;
  display:flex;
}
#u1584 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1584_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1585_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:center;
}
#u1585 {
  border-width:0px;
  position:absolute;
  left:1816px;
  top:79px;
  width:42px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:center;
}
#u1585 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1585_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1586_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u1586 {
  border-width:0px;
  position:absolute;
  left:1809px;
  top:134px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u1586 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1586_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1587_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u1587 {
  border-width:0px;
  position:absolute;
  left:1809px;
  top:178px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u1587 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1587_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1588_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:2px;
}
#u1588 {
  border-width:0px;
  position:absolute;
  left:1772px;
  top:113px;
  width:130px;
  height:1px;
  display:flex;
}
#u1588 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1588_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1589_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u1589 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:122px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u1589 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1589_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1590_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u1590 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:174px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u1590 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1590_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1591_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u1591 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:226px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u1591 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1591_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1592_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1592 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:88px;
  width:70px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1592 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1592_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1593_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1593 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:140px;
  width:84px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1593 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1593_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1594_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1594 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:192px;
  width:129px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1594 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1594_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1595_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1595 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:244px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1595 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1595_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1596_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1596 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:241px;
  width:20px;
  height:20px;
  display:flex;
}
#u1596 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1596_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1597_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1597 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:85px;
  width:20px;
  height:20px;
  display:flex;
}
#u1597 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1597_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1598_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1598 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:137px;
  width:20px;
  height:20px;
  display:flex;
}
#u1598 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1598_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1599_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1599 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:189px;
  width:20px;
  height:20px;
  display:flex;
}
#u1599 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1599_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1600_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1692px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1600 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:80px;
  width:1692px;
  height:60px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1600 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1600_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1601_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:3px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1601 {
  border-width:0px;
  position:absolute;
  left:396px;
  top:137px;
  width:60px;
  height:3px;
  display:flex;
}
#u1601 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1601_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1602_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u1602 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:102px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u1602 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1602_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1603_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
  text-align:center;
}
#u1603 {
  border-width:0px;
  position:absolute;
  left:394px;
  top:102px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
  text-align:center;
}
#u1603 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1603_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1604_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u1604 {
  border-width:0px;
  position:absolute;
  left:518px;
  top:102px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u1604 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1604_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1605_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u1605 {
  border-width:0px;
  position:absolute;
  left:642px;
  top:102px;
  width:80px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u1605 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1605_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1606_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:841px;
  height:911px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1606 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:210px;
  width:841px;
  height:911px;
  display:flex;
  opacity:0.9;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1606 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1606_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1607_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1607 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:225px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1607 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1607_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1608_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:5px;
  height:23px;
}
#u1608 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:223px;
  width:2px;
  height:20px;
  display:flex;
}
#u1608 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1608_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1609_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:415px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1609 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:150px;
  width:415px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1609 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1609_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1610_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1610 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:167px;
  width:125px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1610 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1610_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1611 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1612_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:416px;
  height:50px;
}
#u1612 {
  border-width:0px;
  position:absolute;
  left:1060px;
  top:150px;
  width:416px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1612 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1612_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1613_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
}
#u1613 {
  border-width:0px;
  position:absolute;
  left:1111px;
  top:167px;
  width:96px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
}
#u1613 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1613_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1614_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
}
#u1614 {
  border-width:0px;
  position:absolute;
  left:1090px;
  top:167px;
  width:16px;
  height:16px;
  display:flex;
}
#u1614 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1614_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1615_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:841px;
  height:911px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1615 {
  border-width:0px;
  position:absolute;
  left:1061px;
  top:210px;
  width:841px;
  height:911px;
  display:flex;
  opacity:0.9;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1615 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1615_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1616_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1616 {
  border-width:0px;
  position:absolute;
  left:1076px;
  top:225px;
  width:96px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1616 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1616_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1617_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:5px;
  height:23px;
}
#u1617 {
  border-width:0px;
  position:absolute;
  left:1061px;
  top:223px;
  width:2px;
  height:20px;
  display:flex;
}
#u1617 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1617_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1618 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1619_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:416px;
  height:50px;
}
#u1619 {
  border-width:0px;
  position:absolute;
  left:1486px;
  top:150px;
  width:416px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1619 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1619_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1620_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
}
#u1620 {
  border-width:0px;
  position:absolute;
  left:1537px;
  top:167px;
  width:96px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
}
#u1620 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1620_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1621_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
}
#u1621 {
  border-width:0px;
  position:absolute;
  left:1516px;
  top:167px;
  width:16px;
  height:16px;
  display:flex;
}
#u1621 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1621_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1622_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:811px;
  height:168px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1622 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:270px;
  width:811px;
  height:168px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1622 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1622_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1623_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u1623 {
  border-width:0px;
  position:absolute;
  left:618px;
  top:325px;
  width:65px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u1623 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1623_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1624_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FC6032;
}
#u1624 {
  border-width:0px;
  position:absolute;
  left:618px;
  top:290px;
  width:72px;
  height:25px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FC6032;
}
#u1624 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1624_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1625_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u1625 {
  border-width:0px;
  position:absolute;
  left:771px;
  top:325px;
  width:65px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u1625 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1625_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1626_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#2FC25B;
}
#u1626 {
  border-width:0px;
  position:absolute;
  left:771px;
  top:290px;
  width:66px;
  height:25px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#2FC25B;
}
#u1626 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1626_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1627_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u1627 {
  border-width:0px;
  position:absolute;
  left:443px;
  top:325px;
  width:64px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u1627 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1627_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1628_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:24px;
}
#u1628 {
  border-width:0px;
  position:absolute;
  left:443px;
  top:290px;
  width:94px;
  height:25px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:24px;
}
#u1628 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1628_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1629_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1629 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:308px;
  width:110px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1629 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1629_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1630_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:51px;
}
#u1630 {
  border-width:0px;
  position:absolute;
  left:577px;
  top:290px;
  width:1px;
  height:50px;
  display:flex;
}
#u1630 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1630_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1631_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:51px;
}
#u1631 {
  border-width:0px;
  position:absolute;
  left:730px;
  top:290px;
  width:1px;
  height:50px;
  display:flex;
}
#u1631 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1631_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1632_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u1632 {
  border-width:0px;
  position:absolute;
  left:918px;
  top:325px;
  width:65px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u1632 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1632_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1633_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FC6032;
}
#u1633 {
  border-width:0px;
  position:absolute;
  left:918px;
  top:290px;
  width:72px;
  height:25px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FC6032;
}
#u1633 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1633_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1634_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:51px;
}
#u1634 {
  border-width:0px;
  position:absolute;
  left:877px;
  top:290px;
  width:1px;
  height:50px;
  display:flex;
}
#u1634 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1634_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1635_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u1635 {
  border-width:0px;
  position:absolute;
  left:618px;
  top:404px;
  width:65px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u1635 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1635_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1636_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FC6032;
}
#u1636 {
  border-width:0px;
  position:absolute;
  left:618px;
  top:369px;
  width:72px;
  height:25px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FC6032;
}
#u1636 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1636_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1637_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u1637 {
  border-width:0px;
  position:absolute;
  left:771px;
  top:404px;
  width:65px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u1637 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1637_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1638_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#2FC25B;
}
#u1638 {
  border-width:0px;
  position:absolute;
  left:771px;
  top:369px;
  width:66px;
  height:25px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#2FC25B;
}
#u1638 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1638_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1639_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u1639 {
  border-width:0px;
  position:absolute;
  left:443px;
  top:404px;
  width:64px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u1639 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1639_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1640_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:24px;
}
#u1640 {
  border-width:0px;
  position:absolute;
  left:443px;
  top:369px;
  width:94px;
  height:25px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:24px;
}
#u1640 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1640_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1641_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1641 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:387px;
  width:138px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1641 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1641_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1642_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:51px;
}
#u1642 {
  border-width:0px;
  position:absolute;
  left:577px;
  top:369px;
  width:1px;
  height:50px;
  display:flex;
}
#u1642 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1642_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1643_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:51px;
}
#u1643 {
  border-width:0px;
  position:absolute;
  left:730px;
  top:369px;
  width:1px;
  height:50px;
  display:flex;
}
#u1643 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1643_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1644_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u1644 {
  border-width:0px;
  position:absolute;
  left:918px;
  top:404px;
  width:65px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u1644 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1644_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1645_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FC6032;
}
#u1645 {
  border-width:0px;
  position:absolute;
  left:918px;
  top:369px;
  width:72px;
  height:25px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FC6032;
}
#u1645 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1645_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1646_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:51px;
}
#u1646 {
  border-width:0px;
  position:absolute;
  left:877px;
  top:369px;
  width:1px;
  height:50px;
  display:flex;
}
#u1646 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1646_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1647_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:811px;
  height:356px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1647 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:448px;
  width:811px;
  height:356px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1647 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1647_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1648_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1648 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:463px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1648 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1648_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1649 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1650_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:742px;
  height:2px;
}
#u1650 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:733px;
  width:741px;
  height:1px;
  display:flex;
}
#u1650 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1650_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1651_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:742px;
  height:2px;
}
#u1651 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:667px;
  width:741px;
  height:1px;
  display:flex;
}
#u1651 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1651_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1652_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:742px;
  height:2px;
}
#u1652 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:634px;
  width:741px;
  height:1px;
  display:flex;
}
#u1652 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1652_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1653_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:742px;
  height:2px;
}
#u1653 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:601px;
  width:741px;
  height:1px;
  display:flex;
}
#u1653 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1653_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1654_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:742px;
  height:2px;
}
#u1654 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:568px;
  width:741px;
  height:1px;
  display:flex;
}
#u1654 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1654_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1655_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1655 {
  border-width:0px;
  position:absolute;
  left:263px;
  top:724px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1655 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1655_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1656_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1656 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:625px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1656 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1656_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1657_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1657 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:559px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1657 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1657_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1658_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1658 {
  border-width:0px;
  position:absolute;
  left:315px;
  top:734px;
  width:1px;
  height:5px;
  display:flex;
}
#u1658 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1658_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1659_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1659 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:744px;
  width:31px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1659 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1659_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1660_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1660 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:658px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1660 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1660_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1661_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1661 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:592px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1661 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1661_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1662_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:742px;
  height:2px;
}
#u1662 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:700px;
  width:741px;
  height:1px;
  display:flex;
}
#u1662 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1662_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1663_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1663 {
  border-width:0px;
  position:absolute;
  left:250px;
  top:691px;
  width:20px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1663 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1663_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1664_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1664 {
  border-width:0px;
  position:absolute;
  left:449px;
  top:734px;
  width:1px;
  height:5px;
  display:flex;
}
#u1664 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1664_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1665_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1665 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:744px;
  width:31px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1665 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1665_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1666_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1666 {
  border-width:0px;
  position:absolute;
  left:583px;
  top:734px;
  width:1px;
  height:5px;
  display:flex;
}
#u1666 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1666_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1667_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1667 {
  border-width:0px;
  position:absolute;
  left:568px;
  top:744px;
  width:31px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1667 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1667_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1668_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1668 {
  border-width:0px;
  position:absolute;
  left:717px;
  top:734px;
  width:1px;
  height:5px;
  display:flex;
}
#u1668 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1668_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1669_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1669 {
  border-width:0px;
  position:absolute;
  left:702px;
  top:744px;
  width:31px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1669 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1669_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1670_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1670 {
  border-width:0px;
  position:absolute;
  left:851px;
  top:734px;
  width:1px;
  height:5px;
  display:flex;
}
#u1670 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1670_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1671_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1671 {
  border-width:0px;
  position:absolute;
  left:836px;
  top:744px;
  width:31px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1671 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1671_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1672_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1672 {
  border-width:0px;
  position:absolute;
  left:985px;
  top:734px;
  width:1px;
  height:5px;
  display:flex;
}
#u1672 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1672_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1673_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1673 {
  border-width:0px;
  position:absolute;
  left:970px;
  top:744px;
  width:31px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1673 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1673_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1674_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:185px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1674 {
  border-width:0px;
  position:absolute;
  left:295px;
  top:548px;
  width:20px;
  height:185px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1674 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1674_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1675_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:742px;
  height:2px;
}
#u1675 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:534px;
  width:741px;
  height:1px;
  display:flex;
}
#u1675 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1675_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1676_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1676 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:525px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1676 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1676_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1677_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:742px;
  height:2px;
}
#u1677 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:501px;
  width:741px;
  height:1px;
  display:flex;
}
#u1677 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1677_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1678_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1678 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:492px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1678 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1678_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1679_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:146px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1679 {
  border-width:0px;
  position:absolute;
  left:316px;
  top:587px;
  width:20px;
  height:146px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1679 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1679_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1680_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:185px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1680 {
  border-width:0px;
  position:absolute;
  left:429px;
  top:548px;
  width:20px;
  height:185px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1680 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1680_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1681_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:174px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1681 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:559px;
  width:20px;
  height:174px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1681 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1681_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1682_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:157px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1682 {
  border-width:0px;
  position:absolute;
  left:563px;
  top:576px;
  width:20px;
  height:157px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1682 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1682_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1683_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:174px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1683 {
  border-width:0px;
  position:absolute;
  left:584px;
  top:559px;
  width:20px;
  height:174px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1683 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1683_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1684_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:157px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1684 {
  border-width:0px;
  position:absolute;
  left:697px;
  top:576px;
  width:20px;
  height:157px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1684 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1684_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1685_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:146px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1685 {
  border-width:0px;
  position:absolute;
  left:718px;
  top:587px;
  width:20px;
  height:146px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1685 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1685_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1686_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:185px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1686 {
  border-width:0px;
  position:absolute;
  left:831px;
  top:548px;
  width:20px;
  height:185px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1686 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1686_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1687_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:172px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1687 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:561px;
  width:20px;
  height:172px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1687 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1687_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1688_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:160px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1688 {
  border-width:0px;
  position:absolute;
  left:965px;
  top:573px;
  width:20px;
  height:160px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1688 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1688_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1689_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:172px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1689 {
  border-width:0px;
  position:absolute;
  left:986px;
  top:561px;
  width:20px;
  height:172px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1689 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1689_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1690 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1691 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1692 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1693_img {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-10px;
  width:180px;
  height:79px;
}
#u1693 {
  border-width:0px;
  position:absolute;
  left:460px;
  top:548px;
  width:160px;
  height:59px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1693 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1693_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1694_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1694 {
  border-width:0px;
  position:absolute;
  left:470px;
  top:575px;
  width:68px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1694 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1694_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1695_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u1695 {
  border-width:0px;
  position:absolute;
  left:470px;
  top:558px;
  width:31px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u1695 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1695_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1696_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u1696 {
  border-width:0px;
  position:absolute;
  left:475px;
  top:583px;
  width:6px;
  height:6px;
  display:flex;
}
#u1696 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1696_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1697_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u1697 {
  border-width:0px;
  position:absolute;
  left:486px;
  top:580px;
  width:47px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u1697 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1697_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1698_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1698 {
  border-width:0px;
  position:absolute;
  left:542px;
  top:575px;
  width:68px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1698 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1698_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1699_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u1699 {
  border-width:0px;
  position:absolute;
  left:547px;
  top:583px;
  width:6px;
  height:6px;
  display:flex;
}
#u1699 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1699_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1700_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#2FC25B;
  line-height:12px;
}
#u1700 {
  border-width:0px;
  position:absolute;
  left:558px;
  top:580px;
  width:47px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#2FC25B;
  line-height:12px;
}
#u1700 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1700_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1701_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1701 {
  border-width:0px;
  position:absolute;
  left:495px;
  top:778px;
  width:10px;
  height:10px;
  display:flex;
}
#u1701 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1701_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1702_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1702 {
  border-width:0px;
  position:absolute;
  left:510px;
  top:777px;
  width:94px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1702 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1702_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1703_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u1703 {
  border-width:0px;
  position:absolute;
  left:634px;
  top:778px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u1703 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1703_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1704_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1704 {
  border-width:0px;
  position:absolute;
  left:649px;
  top:777px;
  width:118px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1704 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1704_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1705_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:400px;
  height:292px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1705 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:814px;
  width:400px;
  height:292px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1705 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1705_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1706_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1706 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:829px;
  width:40px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1706 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1706_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1707 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1708_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:338px;
  height:2px;
}
#u1708 {
  border-width:0px;
  position:absolute;
  left:273px;
  top:1033px;
  width:337px;
  height:1px;
  display:flex;
}
#u1708 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1708_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1709_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:338px;
  height:2px;
}
#u1709 {
  border-width:0px;
  position:absolute;
  left:273px;
  top:967px;
  width:337px;
  height:1px;
  display:flex;
}
#u1709 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1709_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1710_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:338px;
  height:2px;
}
#u1710 {
  border-width:0px;
  position:absolute;
  left:273px;
  top:934px;
  width:337px;
  height:1px;
  display:flex;
}
#u1710 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1710_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1711_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:338px;
  height:2px;
}
#u1711 {
  border-width:0px;
  position:absolute;
  left:273px;
  top:901px;
  width:337px;
  height:1px;
  display:flex;
}
#u1711 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1711_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1712_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:338px;
  height:2px;
}
#u1712 {
  border-width:0px;
  position:absolute;
  left:273px;
  top:867px;
  width:337px;
  height:1px;
  display:flex;
}
#u1712 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1712_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1713_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1713 {
  border-width:0px;
  position:absolute;
  left:247px;
  top:1024px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1713 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1713_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1714_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1714 {
  border-width:0px;
  position:absolute;
  left:247px;
  top:925px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1714 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1714_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1715_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1715 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:858px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1715 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1715_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1716_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1716 {
  border-width:0px;
  position:absolute;
  left:288px;
  top:1034px;
  width:1px;
  height:5px;
  display:flex;
}
#u1716 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1716_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1717_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1717 {
  border-width:0px;
  position:absolute;
  left:273px;
  top:1044px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1717 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1717_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1718_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1718 {
  border-width:0px;
  position:absolute;
  left:247px;
  top:958px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1718 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1718_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1719_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1719 {
  border-width:0px;
  position:absolute;
  left:247px;
  top:892px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1719 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1719_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1720_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:338px;
  height:2px;
}
#u1720 {
  border-width:0px;
  position:absolute;
  left:273px;
  top:1000px;
  width:337px;
  height:1px;
  display:flex;
}
#u1720 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1720_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1721_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1721 {
  border-width:0px;
  position:absolute;
  left:247px;
  top:991px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1721 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1721_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1722 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1723_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:340px;
  height:80px;
}
#u1723p000 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:38px;
  width:184px;
  height:22px;
  -webkit-transform:rotate(-17deg);
  -moz-transform:rotate(-17deg);
  -ms-transform:rotate(-17deg);
  transform:rotate(-17deg);
}
#u1723p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:184px;
  height:22px;
}
#u1723p001 {
  border-width:0px;
  position:absolute;
  left:161px;
  top:12px;
  width:112px;
  height:24px;
  -webkit-transform:rotate(-13deg);
  -moz-transform:rotate(-13deg);
  -ms-transform:rotate(-13deg);
  transform:rotate(-13deg);
}
#u1723p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:24px;
}
#u1723p002 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:2px;
  width:76px;
  height:8px;
  -webkit-transform:rotate(-11deg);
  -moz-transform:rotate(-11deg);
  -ms-transform:rotate(-11deg);
  transform:rotate(-11deg);
}
#u1723p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:8px;
}
#u1723.compound {
  width:0px;
  height:0px;
}
#u1723 {
  border-width:0px;
  position:absolute;
  left:273px;
  top:898px;
  width:337px;
  height:77px;
  display:flex;
}
#u1723 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1723_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1724_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:340px;
  height:83px;
}
#u1724 {
  border-width:0px;
  position:absolute;
  left:273px;
  top:895px;
  width:337px;
  height:80px;
  display:flex;
}
#u1724 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1724_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1725_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:168px;
}
#u1725 {
  border-width:0px;
  position:absolute;
  left:409px;
  top:867px;
  width:1px;
  height:167px;
  display:flex;
}
#u1725 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1725_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1726 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1727_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u1727 {
  border-width:0px;
  position:absolute;
  left:405px;
  top:930px;
  width:9px;
  height:9px;
  display:flex;
  color:transparent;
}
#u1727 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1727_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1728 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1729 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1730 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1731_img {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-10px;
  width:94px;
  height:79px;
}
#u1731 {
  border-width:0px;
  position:absolute;
  left:419px;
  top:905px;
  width:74px;
  height:59px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1731 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1731_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1732_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1732 {
  border-width:0px;
  position:absolute;
  left:429px;
  top:932px;
  width:54px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1732 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1732_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1733_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u1733 {
  border-width:0px;
  position:absolute;
  left:429px;
  top:915px;
  width:31px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u1733 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1733_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1734_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u1734 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:940px;
  width:6px;
  height:6px;
  display:flex;
}
#u1734 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1734_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1735_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u1735 {
  border-width:0px;
  position:absolute;
  left:445px;
  top:937px;
  width:33px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u1735 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1735_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1736_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1736 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:1034px;
  width:1px;
  height:5px;
  display:flex;
}
#u1736 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1736_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1737_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1737 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:1044px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1737 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1737_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1738_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1738 {
  border-width:0px;
  position:absolute;
  left:409px;
  top:1034px;
  width:1px;
  height:5px;
  display:flex;
}
#u1738 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1738_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1739_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1739 {
  border-width:0px;
  position:absolute;
  left:394px;
  top:1044px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1739 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1739_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1740_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1740 {
  border-width:0px;
  position:absolute;
  left:470px;
  top:1034px;
  width:1px;
  height:5px;
  display:flex;
}
#u1740 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1740_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1741_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1741 {
  border-width:0px;
  position:absolute;
  left:455px;
  top:1044px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1741 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1741_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1742_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1742 {
  border-width:0px;
  position:absolute;
  left:532px;
  top:1034px;
  width:1px;
  height:5px;
  display:flex;
}
#u1742 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1742_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1743_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1743 {
  border-width:0px;
  position:absolute;
  left:517px;
  top:1044px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1743 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1743_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1744_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1744 {
  border-width:0px;
  position:absolute;
  left:594px;
  top:1034px;
  width:1px;
  height:5px;
  display:flex;
}
#u1744 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1744_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1745_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1745 {
  border-width:0px;
  position:absolute;
  left:579px;
  top:1044px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1745 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1745_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1746_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1746 {
  border-width:0px;
  position:absolute;
  left:289px;
  top:1080px;
  width:10px;
  height:10px;
  display:flex;
}
#u1746 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1746_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1747_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1747 {
  border-width:0px;
  position:absolute;
  left:304px;
  top:1079px;
  width:94px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1747 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1747_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1748_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u1748 {
  border-width:0px;
  position:absolute;
  left:428px;
  top:1080px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u1748 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1748_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1749_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1749 {
  border-width:0px;
  position:absolute;
  left:443px;
  top:1079px;
  width:118px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1749 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1749_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1750_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:401px;
  height:292px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1750 {
  border-width:0px;
  position:absolute;
  left:635px;
  top:814px;
  width:401px;
  height:292px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1750 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1750_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1751_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1751 {
  border-width:0px;
  position:absolute;
  left:650px;
  top:829px;
  width:42px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1751 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1751_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1752 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1753_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:339px;
  height:2px;
}
#u1753 {
  border-width:0px;
  position:absolute;
  left:683px;
  top:1033px;
  width:338px;
  height:1px;
  display:flex;
}
#u1753 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1753_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1754_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:339px;
  height:2px;
}
#u1754 {
  border-width:0px;
  position:absolute;
  left:683px;
  top:967px;
  width:338px;
  height:1px;
  display:flex;
}
#u1754 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1754_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1755_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:339px;
  height:2px;
}
#u1755 {
  border-width:0px;
  position:absolute;
  left:683px;
  top:934px;
  width:338px;
  height:1px;
  display:flex;
}
#u1755 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1755_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1756_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:339px;
  height:2px;
}
#u1756 {
  border-width:0px;
  position:absolute;
  left:683px;
  top:901px;
  width:338px;
  height:1px;
  display:flex;
}
#u1756 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1756_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1757_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:339px;
  height:2px;
}
#u1757 {
  border-width:0px;
  position:absolute;
  left:683px;
  top:867px;
  width:338px;
  height:1px;
  display:flex;
}
#u1757 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1757_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1758_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1758 {
  border-width:0px;
  position:absolute;
  left:657px;
  top:1024px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1758 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1758_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1759_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1759 {
  border-width:0px;
  position:absolute;
  left:657px;
  top:925px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1759 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1759_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1760_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1760 {
  border-width:0px;
  position:absolute;
  left:650px;
  top:858px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1760 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1760_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1761_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1761 {
  border-width:0px;
  position:absolute;
  left:698px;
  top:1034px;
  width:1px;
  height:5px;
  display:flex;
}
#u1761 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1761_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1762_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1762 {
  border-width:0px;
  position:absolute;
  left:683px;
  top:1044px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1762 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1762_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1763_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1763 {
  border-width:0px;
  position:absolute;
  left:657px;
  top:958px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1763 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1763_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1764_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1764 {
  border-width:0px;
  position:absolute;
  left:657px;
  top:892px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1764 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1764_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1765_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:339px;
  height:2px;
}
#u1765 {
  border-width:0px;
  position:absolute;
  left:683px;
  top:1000px;
  width:338px;
  height:1px;
  display:flex;
}
#u1765 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1765_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1766_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1766 {
  border-width:0px;
  position:absolute;
  left:657px;
  top:991px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1766 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1766_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1767 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1768_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:341px;
  height:80px;
}
#u1768p000 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:38px;
  width:184px;
  height:22px;
  -webkit-transform:rotate(-17deg);
  -moz-transform:rotate(-17deg);
  -ms-transform:rotate(-17deg);
  transform:rotate(-17deg);
}
#u1768p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:184px;
  height:22px;
}
#u1768p001 {
  border-width:0px;
  position:absolute;
  left:163px;
  top:12px;
  width:110px;
  height:24px;
  -webkit-transform:rotate(-13deg);
  -moz-transform:rotate(-13deg);
  -ms-transform:rotate(-13deg);
  transform:rotate(-13deg);
}
#u1768p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:24px;
}
#u1768p002 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:2px;
  width:76px;
  height:8px;
  -webkit-transform:rotate(-11deg);
  -moz-transform:rotate(-11deg);
  -ms-transform:rotate(-11deg);
  transform:rotate(-11deg);
}
#u1768p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:8px;
}
#u1768.compound {
  width:0px;
  height:0px;
}
#u1768 {
  border-width:0px;
  position:absolute;
  left:683px;
  top:898px;
  width:338px;
  height:77px;
  display:flex;
}
#u1768 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1768_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1769_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:341px;
  height:83px;
}
#u1769 {
  border-width:0px;
  position:absolute;
  left:683px;
  top:895px;
  width:338px;
  height:80px;
  display:flex;
}
#u1769 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1769_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1770_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:168px;
}
#u1770 {
  border-width:0px;
  position:absolute;
  left:819px;
  top:867px;
  width:1px;
  height:167px;
  display:flex;
}
#u1770 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1770_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1771 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1772_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u1772 {
  border-width:0px;
  position:absolute;
  left:815px;
  top:930px;
  width:9px;
  height:9px;
  display:flex;
  color:transparent;
}
#u1772 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1772_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1773 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1774 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1775 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1776_img {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-10px;
  width:94px;
  height:79px;
}
#u1776 {
  border-width:0px;
  position:absolute;
  left:829px;
  top:905px;
  width:74px;
  height:59px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1776 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1776_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1777_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1777 {
  border-width:0px;
  position:absolute;
  left:839px;
  top:932px;
  width:54px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1777 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1777_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1778_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u1778 {
  border-width:0px;
  position:absolute;
  left:839px;
  top:915px;
  width:31px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u1778 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1778_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1779_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u1779 {
  border-width:0px;
  position:absolute;
  left:844px;
  top:940px;
  width:6px;
  height:6px;
  display:flex;
}
#u1779 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1779_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1780_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u1780 {
  border-width:0px;
  position:absolute;
  left:855px;
  top:937px;
  width:33px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u1780 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1780_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1781_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1781 {
  border-width:0px;
  position:absolute;
  left:757px;
  top:1034px;
  width:1px;
  height:5px;
  display:flex;
}
#u1781 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1781_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1782_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1782 {
  border-width:0px;
  position:absolute;
  left:742px;
  top:1044px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1782 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1782_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1783_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1783 {
  border-width:0px;
  position:absolute;
  left:819px;
  top:1034px;
  width:1px;
  height:5px;
  display:flex;
}
#u1783 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1783_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1784_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1784 {
  border-width:0px;
  position:absolute;
  left:804px;
  top:1044px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1784 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1784_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1785_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1785 {
  border-width:0px;
  position:absolute;
  left:880px;
  top:1034px;
  width:1px;
  height:5px;
  display:flex;
}
#u1785 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1785_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1786_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1786 {
  border-width:0px;
  position:absolute;
  left:865px;
  top:1044px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1786 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1786_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1787_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1787 {
  border-width:0px;
  position:absolute;
  left:942px;
  top:1034px;
  width:1px;
  height:5px;
  display:flex;
}
#u1787 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1787_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1788_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1788 {
  border-width:0px;
  position:absolute;
  left:927px;
  top:1044px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1788 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1788_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1789_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1789 {
  border-width:0px;
  position:absolute;
  left:1004px;
  top:1034px;
  width:1px;
  height:5px;
  display:flex;
}
#u1789 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1789_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1790_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1790 {
  border-width:0px;
  position:absolute;
  left:989px;
  top:1044px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1790 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1790_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1791_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1791 {
  border-width:0px;
  position:absolute;
  left:699px;
  top:1080px;
  width:10px;
  height:10px;
  display:flex;
}
#u1791 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1791_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1792_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1792 {
  border-width:0px;
  position:absolute;
  left:714px;
  top:1079px;
  width:94px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1792 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1792_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1793_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u1793 {
  border-width:0px;
  position:absolute;
  left:838px;
  top:1080px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u1793 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1793_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1794_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1794 {
  border-width:0px;
  position:absolute;
  left:853px;
  top:1079px;
  width:118px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1794 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1794_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1795_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:400px;
  height:292px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1795 {
  border-width:0px;
  position:absolute;
  left:1076px;
  top:270px;
  width:400px;
  height:292px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1795 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1795_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1796_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1796 {
  border-width:0px;
  position:absolute;
  left:1091px;
  top:285px;
  width:42px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1796 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1796_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1797 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1798_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:338px;
  height:2px;
}
#u1798 {
  border-width:0px;
  position:absolute;
  left:1124px;
  top:489px;
  width:337px;
  height:1px;
  display:flex;
}
#u1798 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1798_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1799_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:338px;
  height:2px;
}
#u1799 {
  border-width:0px;
  position:absolute;
  left:1124px;
  top:423px;
  width:337px;
  height:1px;
  display:flex;
}
#u1799 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1799_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1800_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:338px;
  height:2px;
}
#u1800 {
  border-width:0px;
  position:absolute;
  left:1124px;
  top:390px;
  width:337px;
  height:1px;
  display:flex;
}
#u1800 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1800_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1801_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:338px;
  height:2px;
}
#u1801 {
  border-width:0px;
  position:absolute;
  left:1124px;
  top:357px;
  width:337px;
  height:1px;
  display:flex;
}
#u1801 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1801_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1802_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:338px;
  height:2px;
}
#u1802 {
  border-width:0px;
  position:absolute;
  left:1124px;
  top:323px;
  width:337px;
  height:1px;
  display:flex;
}
#u1802 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1802_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1803_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1803 {
  border-width:0px;
  position:absolute;
  left:1098px;
  top:480px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1803 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1803_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1804_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1804 {
  border-width:0px;
  position:absolute;
  left:1098px;
  top:381px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1804 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1804_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1805_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1805 {
  border-width:0px;
  position:absolute;
  left:1091px;
  top:314px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1805 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1805_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1806_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1806 {
  border-width:0px;
  position:absolute;
  left:1139px;
  top:490px;
  width:1px;
  height:5px;
  display:flex;
}
#u1806 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1806_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1807_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1807 {
  border-width:0px;
  position:absolute;
  left:1124px;
  top:500px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1807 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1807_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1808_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1808 {
  border-width:0px;
  position:absolute;
  left:1098px;
  top:414px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1808 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1808_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1809_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1809 {
  border-width:0px;
  position:absolute;
  left:1098px;
  top:348px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1809 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1809_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1810_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:338px;
  height:2px;
}
#u1810 {
  border-width:0px;
  position:absolute;
  left:1124px;
  top:456px;
  width:337px;
  height:1px;
  display:flex;
}
#u1810 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1810_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1811_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1811 {
  border-width:0px;
  position:absolute;
  left:1098px;
  top:447px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1811 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1811_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1812 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1813_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:340px;
  height:80px;
}
#u1813p000 {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:38px;
  width:186px;
  height:22px;
  -webkit-transform:rotate(-17deg);
  -moz-transform:rotate(-17deg);
  -ms-transform:rotate(-17deg);
  transform:rotate(-17deg);
}
#u1813p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:22px;
}
#u1813p001 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:12px;
  width:110px;
  height:24px;
  -webkit-transform:rotate(-13deg);
  -moz-transform:rotate(-13deg);
  -ms-transform:rotate(-13deg);
  transform:rotate(-13deg);
}
#u1813p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:24px;
}
#u1813p002 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:2px;
  width:76px;
  height:8px;
  -webkit-transform:rotate(-11deg);
  -moz-transform:rotate(-11deg);
  -ms-transform:rotate(-11deg);
  transform:rotate(-11deg);
}
#u1813p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:8px;
}
#u1813.compound {
  width:0px;
  height:0px;
}
#u1813 {
  border-width:0px;
  position:absolute;
  left:1124px;
  top:354px;
  width:337px;
  height:77px;
  display:flex;
}
#u1813 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1813_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1814_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:340px;
  height:83px;
}
#u1814 {
  border-width:0px;
  position:absolute;
  left:1124px;
  top:351px;
  width:337px;
  height:80px;
  display:flex;
}
#u1814 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1814_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1815_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:168px;
}
#u1815 {
  border-width:0px;
  position:absolute;
  left:1260px;
  top:323px;
  width:1px;
  height:167px;
  display:flex;
}
#u1815 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1815_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1816 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1817_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u1817 {
  border-width:0px;
  position:absolute;
  left:1256px;
  top:386px;
  width:9px;
  height:9px;
  display:flex;
  color:transparent;
}
#u1817 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1817_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1818 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1819 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1820 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1821_img {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-10px;
  width:94px;
  height:79px;
}
#u1821 {
  border-width:0px;
  position:absolute;
  left:1270px;
  top:361px;
  width:74px;
  height:59px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1821 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1821_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1822_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1822 {
  border-width:0px;
  position:absolute;
  left:1280px;
  top:388px;
  width:54px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1822 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1822_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1823_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u1823 {
  border-width:0px;
  position:absolute;
  left:1280px;
  top:371px;
  width:31px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u1823 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1823_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1824_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u1824 {
  border-width:0px;
  position:absolute;
  left:1285px;
  top:396px;
  width:6px;
  height:6px;
  display:flex;
}
#u1824 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1824_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1825_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u1825 {
  border-width:0px;
  position:absolute;
  left:1296px;
  top:393px;
  width:33px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u1825 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1825_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1826_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1826 {
  border-width:0px;
  position:absolute;
  left:1198px;
  top:490px;
  width:1px;
  height:5px;
  display:flex;
}
#u1826 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1826_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1827_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1827 {
  border-width:0px;
  position:absolute;
  left:1183px;
  top:500px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1827 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1827_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1828_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1828 {
  border-width:0px;
  position:absolute;
  left:1260px;
  top:490px;
  width:1px;
  height:5px;
  display:flex;
}
#u1828 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1828_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1829_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1829 {
  border-width:0px;
  position:absolute;
  left:1245px;
  top:500px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1829 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1829_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1830_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1830 {
  border-width:0px;
  position:absolute;
  left:1321px;
  top:490px;
  width:1px;
  height:5px;
  display:flex;
}
#u1830 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1830_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1831_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1831 {
  border-width:0px;
  position:absolute;
  left:1306px;
  top:500px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1831 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1831_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1832_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1832 {
  border-width:0px;
  position:absolute;
  left:1383px;
  top:490px;
  width:1px;
  height:5px;
  display:flex;
}
#u1832 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1832_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1833_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1833 {
  border-width:0px;
  position:absolute;
  left:1368px;
  top:500px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1833 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1833_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1834_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1834 {
  border-width:0px;
  position:absolute;
  left:1445px;
  top:490px;
  width:1px;
  height:5px;
  display:flex;
}
#u1834 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1834_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1835_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1835 {
  border-width:0px;
  position:absolute;
  left:1430px;
  top:500px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1835 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1835_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1836_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1836 {
  border-width:0px;
  position:absolute;
  left:1140px;
  top:536px;
  width:10px;
  height:10px;
  display:flex;
}
#u1836 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1836_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1837_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1837 {
  border-width:0px;
  position:absolute;
  left:1155px;
  top:535px;
  width:94px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1837 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1837_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1838_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u1838 {
  border-width:0px;
  position:absolute;
  left:1279px;
  top:536px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u1838 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1838_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1839_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1839 {
  border-width:0px;
  position:absolute;
  left:1294px;
  top:535px;
  width:118px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1839 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1839_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1840_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:401px;
  height:292px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1840 {
  border-width:0px;
  position:absolute;
  left:1486px;
  top:270px;
  width:401px;
  height:292px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1840 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1840_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1841_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1841 {
  border-width:0px;
  position:absolute;
  left:1501px;
  top:285px;
  width:42px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1841 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1841_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1842 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1843_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:339px;
  height:2px;
}
#u1843 {
  border-width:0px;
  position:absolute;
  left:1534px;
  top:489px;
  width:338px;
  height:1px;
  display:flex;
}
#u1843 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1843_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1844_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:339px;
  height:2px;
}
#u1844 {
  border-width:0px;
  position:absolute;
  left:1534px;
  top:423px;
  width:338px;
  height:1px;
  display:flex;
}
#u1844 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1844_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1845_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:339px;
  height:2px;
}
#u1845 {
  border-width:0px;
  position:absolute;
  left:1534px;
  top:390px;
  width:338px;
  height:1px;
  display:flex;
}
#u1845 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1845_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1846_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:339px;
  height:2px;
}
#u1846 {
  border-width:0px;
  position:absolute;
  left:1534px;
  top:357px;
  width:338px;
  height:1px;
  display:flex;
}
#u1846 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1846_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1847_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:339px;
  height:2px;
}
#u1847 {
  border-width:0px;
  position:absolute;
  left:1534px;
  top:323px;
  width:338px;
  height:1px;
  display:flex;
}
#u1847 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1847_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1848_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1848 {
  border-width:0px;
  position:absolute;
  left:1508px;
  top:480px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1848 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1848_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1849_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1849 {
  border-width:0px;
  position:absolute;
  left:1508px;
  top:381px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1849 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1849_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1850_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1850 {
  border-width:0px;
  position:absolute;
  left:1501px;
  top:314px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1850 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1850_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1851_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1851 {
  border-width:0px;
  position:absolute;
  left:1549px;
  top:490px;
  width:1px;
  height:5px;
  display:flex;
}
#u1851 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1851_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1852_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1852 {
  border-width:0px;
  position:absolute;
  left:1534px;
  top:500px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1852 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1852_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1853_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1853 {
  border-width:0px;
  position:absolute;
  left:1508px;
  top:414px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1853 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1853_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1854_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1854 {
  border-width:0px;
  position:absolute;
  left:1508px;
  top:348px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1854 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1854_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1855_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:339px;
  height:2px;
}
#u1855 {
  border-width:0px;
  position:absolute;
  left:1534px;
  top:456px;
  width:338px;
  height:1px;
  display:flex;
}
#u1855 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1855_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1856_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1856 {
  border-width:0px;
  position:absolute;
  left:1508px;
  top:447px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1856 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1856_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1857 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1858_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:341px;
  height:80px;
}
#u1858p000 {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:38px;
  width:186px;
  height:22px;
  -webkit-transform:rotate(-17deg);
  -moz-transform:rotate(-17deg);
  -ms-transform:rotate(-17deg);
  transform:rotate(-17deg);
}
#u1858p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:22px;
}
#u1858p001 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:12px;
  width:112px;
  height:24px;
  -webkit-transform:rotate(-13deg);
  -moz-transform:rotate(-13deg);
  -ms-transform:rotate(-13deg);
  transform:rotate(-13deg);
}
#u1858p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:24px;
}
#u1858p002 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:2px;
  width:76px;
  height:8px;
  -webkit-transform:rotate(-11deg);
  -moz-transform:rotate(-11deg);
  -ms-transform:rotate(-11deg);
  transform:rotate(-11deg);
}
#u1858p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:8px;
}
#u1858.compound {
  width:0px;
  height:0px;
}
#u1858 {
  border-width:0px;
  position:absolute;
  left:1534px;
  top:354px;
  width:338px;
  height:77px;
  display:flex;
}
#u1858 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1858_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1859_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:341px;
  height:83px;
}
#u1859 {
  border-width:0px;
  position:absolute;
  left:1534px;
  top:351px;
  width:338px;
  height:80px;
  display:flex;
}
#u1859 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1859_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1860_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:168px;
}
#u1860 {
  border-width:0px;
  position:absolute;
  left:1670px;
  top:323px;
  width:1px;
  height:167px;
  display:flex;
}
#u1860 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1860_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1861 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1862_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u1862 {
  border-width:0px;
  position:absolute;
  left:1666px;
  top:386px;
  width:9px;
  height:9px;
  display:flex;
  color:transparent;
}
#u1862 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1862_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1863 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1864 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1865 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1866_img {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-10px;
  width:94px;
  height:79px;
}
#u1866 {
  border-width:0px;
  position:absolute;
  left:1680px;
  top:361px;
  width:74px;
  height:59px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1866 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1866_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1867_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1867 {
  border-width:0px;
  position:absolute;
  left:1690px;
  top:388px;
  width:54px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1867 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1867_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1868_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u1868 {
  border-width:0px;
  position:absolute;
  left:1690px;
  top:371px;
  width:31px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u1868 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1868_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1869_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u1869 {
  border-width:0px;
  position:absolute;
  left:1695px;
  top:396px;
  width:6px;
  height:6px;
  display:flex;
}
#u1869 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1869_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1870_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u1870 {
  border-width:0px;
  position:absolute;
  left:1706px;
  top:393px;
  width:33px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u1870 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1870_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1871_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1871 {
  border-width:0px;
  position:absolute;
  left:1608px;
  top:490px;
  width:1px;
  height:5px;
  display:flex;
}
#u1871 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1871_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1872_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1872 {
  border-width:0px;
  position:absolute;
  left:1593px;
  top:500px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1872 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1872_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1873_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1873 {
  border-width:0px;
  position:absolute;
  left:1670px;
  top:490px;
  width:1px;
  height:5px;
  display:flex;
}
#u1873 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1873_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1874_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1874 {
  border-width:0px;
  position:absolute;
  left:1655px;
  top:500px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1874 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1874_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1875_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1875 {
  border-width:0px;
  position:absolute;
  left:1731px;
  top:490px;
  width:1px;
  height:5px;
  display:flex;
}
#u1875 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1875_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1876_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1876 {
  border-width:0px;
  position:absolute;
  left:1716px;
  top:500px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1876 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1876_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1877_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1877 {
  border-width:0px;
  position:absolute;
  left:1793px;
  top:490px;
  width:1px;
  height:5px;
  display:flex;
}
#u1877 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1877_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1878_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1878 {
  border-width:0px;
  position:absolute;
  left:1778px;
  top:500px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1878 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1878_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1879_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1879 {
  border-width:0px;
  position:absolute;
  left:1855px;
  top:490px;
  width:1px;
  height:5px;
  display:flex;
}
#u1879 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1879_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1880_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1880 {
  border-width:0px;
  position:absolute;
  left:1840px;
  top:500px;
  width:31px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1880 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1880_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1881_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1881 {
  border-width:0px;
  position:absolute;
  left:1550px;
  top:536px;
  width:10px;
  height:10px;
  display:flex;
}
#u1881 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1881_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1882_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1882 {
  border-width:0px;
  position:absolute;
  left:1565px;
  top:535px;
  width:94px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1882 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1882_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1883_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u1883 {
  border-width:0px;
  position:absolute;
  left:1689px;
  top:536px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u1883 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1883_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1884_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1884 {
  border-width:0px;
  position:absolute;
  left:1704px;
  top:535px;
  width:118px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1884 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1884_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1885_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:811px;
  height:356px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1885 {
  border-width:0px;
  position:absolute;
  left:1076px;
  top:572px;
  width:811px;
  height:356px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1885 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1885_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1886_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1886 {
  border-width:0px;
  position:absolute;
  left:1091px;
  top:587px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1886 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1886_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1887 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1888_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:742px;
  height:2px;
}
#u1888 {
  border-width:0px;
  position:absolute;
  left:1131px;
  top:857px;
  width:741px;
  height:1px;
  display:flex;
}
#u1888 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1888_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1889_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:742px;
  height:2px;
}
#u1889 {
  border-width:0px;
  position:absolute;
  left:1131px;
  top:791px;
  width:741px;
  height:1px;
  display:flex;
}
#u1889 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1889_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1890_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:742px;
  height:2px;
}
#u1890 {
  border-width:0px;
  position:absolute;
  left:1131px;
  top:758px;
  width:741px;
  height:1px;
  display:flex;
}
#u1890 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1890_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1891_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:742px;
  height:2px;
}
#u1891 {
  border-width:0px;
  position:absolute;
  left:1131px;
  top:725px;
  width:741px;
  height:1px;
  display:flex;
}
#u1891 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1891_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1892_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:742px;
  height:2px;
}
#u1892 {
  border-width:0px;
  position:absolute;
  left:1131px;
  top:692px;
  width:741px;
  height:1px;
  display:flex;
}
#u1892 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1892_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1893_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1893 {
  border-width:0px;
  position:absolute;
  left:1114px;
  top:848px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1893 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1893_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1894_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1894 {
  border-width:0px;
  position:absolute;
  left:1091px;
  top:749px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1894 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1894_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1895_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1895 {
  border-width:0px;
  position:absolute;
  left:1091px;
  top:683px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1895 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1895_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1896_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1896 {
  border-width:0px;
  position:absolute;
  left:1166px;
  top:858px;
  width:1px;
  height:5px;
  display:flex;
}
#u1896 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1896_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1897_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1897 {
  border-width:0px;
  position:absolute;
  left:1151px;
  top:868px;
  width:31px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1897 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1897_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1898_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1898 {
  border-width:0px;
  position:absolute;
  left:1091px;
  top:782px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1898 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1898_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1899_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1899 {
  border-width:0px;
  position:absolute;
  left:1091px;
  top:716px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1899 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1899_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1900_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:742px;
  height:2px;
}
#u1900 {
  border-width:0px;
  position:absolute;
  left:1131px;
  top:824px;
  width:741px;
  height:1px;
  display:flex;
}
#u1900 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1900_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1901_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1901 {
  border-width:0px;
  position:absolute;
  left:1101px;
  top:815px;
  width:20px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1901 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1901_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1902_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1902 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:858px;
  width:1px;
  height:5px;
  display:flex;
}
#u1902 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1902_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1903_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1903 {
  border-width:0px;
  position:absolute;
  left:1285px;
  top:868px;
  width:31px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1903 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1903_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1904_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1904 {
  border-width:0px;
  position:absolute;
  left:1434px;
  top:858px;
  width:1px;
  height:5px;
  display:flex;
}
#u1904 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1904_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1905_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1905 {
  border-width:0px;
  position:absolute;
  left:1419px;
  top:868px;
  width:31px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1905 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1905_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1906_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1906 {
  border-width:0px;
  position:absolute;
  left:1568px;
  top:858px;
  width:1px;
  height:5px;
  display:flex;
}
#u1906 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1906_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1907_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1907 {
  border-width:0px;
  position:absolute;
  left:1553px;
  top:868px;
  width:31px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1907 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1907_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1908_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1908 {
  border-width:0px;
  position:absolute;
  left:1702px;
  top:858px;
  width:1px;
  height:5px;
  display:flex;
}
#u1908 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1908_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1909_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1909 {
  border-width:0px;
  position:absolute;
  left:1687px;
  top:868px;
  width:31px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1909 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1909_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1910_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1910 {
  border-width:0px;
  position:absolute;
  left:1836px;
  top:858px;
  width:1px;
  height:5px;
  display:flex;
}
#u1910 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1910_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1911_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1911 {
  border-width:0px;
  position:absolute;
  left:1821px;
  top:868px;
  width:31px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1911 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1911_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1912_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:185px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1912 {
  border-width:0px;
  position:absolute;
  left:1146px;
  top:672px;
  width:20px;
  height:185px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1912 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1912_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1913_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:742px;
  height:2px;
}
#u1913 {
  border-width:0px;
  position:absolute;
  left:1131px;
  top:658px;
  width:741px;
  height:1px;
  display:flex;
}
#u1913 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1913_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1914_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1914 {
  border-width:0px;
  position:absolute;
  left:1091px;
  top:649px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1914 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1914_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1915_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:742px;
  height:2px;
}
#u1915 {
  border-width:0px;
  position:absolute;
  left:1131px;
  top:625px;
  width:741px;
  height:1px;
  display:flex;
}
#u1915 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1915_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1916_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1916 {
  border-width:0px;
  position:absolute;
  left:1091px;
  top:616px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1916 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1916_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1917_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:146px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1917 {
  border-width:0px;
  position:absolute;
  left:1167px;
  top:711px;
  width:20px;
  height:146px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1917 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1917_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1918_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:185px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1918 {
  border-width:0px;
  position:absolute;
  left:1280px;
  top:672px;
  width:20px;
  height:185px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1918 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1918_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1919_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:174px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1919 {
  border-width:0px;
  position:absolute;
  left:1301px;
  top:683px;
  width:20px;
  height:174px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1919 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1919_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1920_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:157px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1920 {
  border-width:0px;
  position:absolute;
  left:1414px;
  top:700px;
  width:20px;
  height:157px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1920 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1920_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1921_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:174px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1921 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:683px;
  width:20px;
  height:174px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1921 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1921_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1922_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:157px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1922 {
  border-width:0px;
  position:absolute;
  left:1548px;
  top:700px;
  width:20px;
  height:157px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1922 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1922_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1923_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:146px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1923 {
  border-width:0px;
  position:absolute;
  left:1569px;
  top:711px;
  width:20px;
  height:146px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1923 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1923_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1924_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:185px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1924 {
  border-width:0px;
  position:absolute;
  left:1682px;
  top:672px;
  width:20px;
  height:185px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1924 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1924_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1925_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:172px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1925 {
  border-width:0px;
  position:absolute;
  left:1703px;
  top:685px;
  width:20px;
  height:172px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1925 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1925_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1926_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:160px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1926 {
  border-width:0px;
  position:absolute;
  left:1816px;
  top:697px;
  width:20px;
  height:160px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1926 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1926_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1927_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:172px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1927 {
  border-width:0px;
  position:absolute;
  left:1837px;
  top:685px;
  width:20px;
  height:172px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1927 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1927_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1928 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1929 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1930 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1931_img {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-10px;
  width:180px;
  height:79px;
}
#u1931 {
  border-width:0px;
  position:absolute;
  left:1311px;
  top:672px;
  width:160px;
  height:59px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1931 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1931_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1932_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1932 {
  border-width:0px;
  position:absolute;
  left:1321px;
  top:699px;
  width:68px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1932 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1932_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1933_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u1933 {
  border-width:0px;
  position:absolute;
  left:1321px;
  top:682px;
  width:31px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u1933 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1933_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1934_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u1934 {
  border-width:0px;
  position:absolute;
  left:1326px;
  top:707px;
  width:6px;
  height:6px;
  display:flex;
}
#u1934 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1934_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1935_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u1935 {
  border-width:0px;
  position:absolute;
  left:1337px;
  top:704px;
  width:47px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u1935 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1935_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1936_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1936 {
  border-width:0px;
  position:absolute;
  left:1393px;
  top:699px;
  width:68px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1936 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1936_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1937_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u1937 {
  border-width:0px;
  position:absolute;
  left:1398px;
  top:707px;
  width:6px;
  height:6px;
  display:flex;
}
#u1937 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1937_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1938_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#2FC25B;
  line-height:12px;
}
#u1938 {
  border-width:0px;
  position:absolute;
  left:1409px;
  top:704px;
  width:47px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#2FC25B;
  line-height:12px;
}
#u1938 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1938_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1939_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1939 {
  border-width:0px;
  position:absolute;
  left:1346px;
  top:902px;
  width:10px;
  height:10px;
  display:flex;
}
#u1939 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1939_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1940_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1940 {
  border-width:0px;
  position:absolute;
  left:1361px;
  top:901px;
  width:94px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1940 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1940_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1941_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u1941 {
  border-width:0px;
  position:absolute;
  left:1485px;
  top:902px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u1941 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1941_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1942_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1942 {
  border-width:0px;
  position:absolute;
  left:1500px;
  top:901px;
  width:118px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1942 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1942_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1943_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:77px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:20px;
}
#u1943 {
  border-width:0px;
  position:absolute;
  left:1182px;
  top:225px;
  width:138px;
  height:77px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:20px;
}
#u1943 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1943_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1944_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
  text-align:center;
}
#u1944 {
  border-width:0px;
  position:absolute;
  left:573px;
  top:167px;
  width:32px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
  text-align:center;
}
#u1944 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1944_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1945_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:415px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1945 {
  border-width:0px;
  position:absolute;
  left:635px;
  top:150px;
  width:415px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1945 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1945_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1946_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1946 {
  border-width:0px;
  position:absolute;
  left:665px;
  top:167px;
  width:157px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1946 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1946_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1947_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
  text-align:center;
}
#u1947 {
  border-width:0px;
  position:absolute;
  left:956px;
  top:167px;
  width:32px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
  text-align:center;
}
#u1947 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1947_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1948 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1949_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:40px;
}
#u1949 {
  border-width:0px;
  position:absolute;
  left:853px;
  top:220px;
  width:198px;
  height:40px;
  display:flex;
}
#u1949 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1949_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1950_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u1950 {
  border-width:0px;
  position:absolute;
  left:868px;
  top:233px;
  width:112px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u1950 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1950_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1951_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1951 {
  border-width:0px;
  position:absolute;
  left:990px;
  top:225px;
  width:30px;
  height:30px;
  display:flex;
}
#u1951 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1951_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1952_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:20px;
}
#u1952 {
  border-width:0px;
  position:absolute;
  left:996px;
  top:230px;
  width:18px;
  height:20px;
  display:flex;
}
#u1952 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1952_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1953_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:10px;
}
#u1953 {
  border-width:0px;
  position:absolute;
  left:1030px;
  top:235px;
  width:6px;
  height:10px;
  display:flex;
}
#u1953 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1953_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1954 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1955_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:40px;
}
#u1955 {
  border-width:0px;
  position:absolute;
  left:1704px;
  top:220px;
  width:198px;
  height:40px;
  display:flex;
}
#u1955 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1955_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1956_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u1956 {
  border-width:0px;
  position:absolute;
  left:1719px;
  top:233px;
  width:112px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u1956 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1956_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1957_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1957 {
  border-width:0px;
  position:absolute;
  left:1841px;
  top:225px;
  width:30px;
  height:30px;
  display:flex;
}
#u1957 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1957_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1958_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:20px;
}
#u1958 {
  border-width:0px;
  position:absolute;
  left:1847px;
  top:230px;
  width:18px;
  height:20px;
  display:flex;
}
#u1958 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1958_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1959_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:10px;
}
#u1959 {
  border-width:0px;
  position:absolute;
  left:1881px;
  top:235px;
  width:6px;
  height:10px;
  display:flex;
}
#u1959 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1959_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1960 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1961_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:1117px;
  background:inherit;
  background-color:rgba(51, 51, 51, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1961 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:1117px;
  display:flex;
  opacity:0.5;
}
#u1961 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1961_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1962 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1963_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:900px;
  height:570px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1963 {
  border-width:0px;
  position:absolute;
  left:506px;
  top:205px;
  width:900px;
  height:570px;
  display:flex;
}
#u1963 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1963_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1964 {
  border-width:0px;
  position:absolute;
  left:536px;
  top:387px;
  width:840px;
  height:204px;
}
#u1965_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:34px;
}
#u1965 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
}
#u1965 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1965_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1966_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:34px;
}
#u1966 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:0px;
  width:183px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
}
#u1966 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1966_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1967_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:451px;
  height:34px;
}
#u1967 {
  border-width:0px;
  position:absolute;
  left:217px;
  top:0px;
  width:451px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
}
#u1967 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1967_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1968_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:34px;
}
#u1968 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:0px;
  width:172px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
}
#u1968 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1968_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1969_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:34px;
}
#u1969 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:34px;
  width:34px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1969 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1969_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1970_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:34px;
}
#u1970 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:34px;
  width:183px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1970 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1970_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1971_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:451px;
  height:34px;
}
#u1971 {
  border-width:0px;
  position:absolute;
  left:217px;
  top:34px;
  width:451px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1971 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1971_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1972_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:34px;
}
#u1972 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:34px;
  width:172px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1972 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1972_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1973_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:34px;
}
#u1973 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:68px;
  width:34px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1973 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1973_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1974_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:34px;
}
#u1974 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:68px;
  width:183px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1974 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1974_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1975_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:451px;
  height:34px;
}
#u1975 {
  border-width:0px;
  position:absolute;
  left:217px;
  top:68px;
  width:451px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1975 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1975_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1976_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:34px;
}
#u1976 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:68px;
  width:172px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1976 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1976_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1977_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:34px;
}
#u1977 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:102px;
  width:34px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1977 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1977_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1978_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:34px;
}
#u1978 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:102px;
  width:183px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1978 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1978_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1979_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:451px;
  height:34px;
}
#u1979 {
  border-width:0px;
  position:absolute;
  left:217px;
  top:102px;
  width:451px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1979 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1979_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1980_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:34px;
}
#u1980 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:102px;
  width:172px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1980 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1980_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1981_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:34px;
}
#u1981 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:136px;
  width:34px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1981 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1981_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1982_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:34px;
}
#u1982 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:136px;
  width:183px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1982 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1982_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1983_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:451px;
  height:34px;
}
#u1983 {
  border-width:0px;
  position:absolute;
  left:217px;
  top:136px;
  width:451px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1983 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1983_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1984_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:34px;
}
#u1984 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:136px;
  width:172px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1984 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1984_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1985_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:34px;
}
#u1985 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:170px;
  width:34px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1985 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1985_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1986_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:34px;
}
#u1986 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:170px;
  width:183px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1986 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1986_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1987_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:451px;
  height:34px;
}
#u1987 {
  border-width:0px;
  position:absolute;
  left:217px;
  top:170px;
  width:451px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1987 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1987_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1988_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:34px;
}
#u1988 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:170px;
  width:172px;
  height:34px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1988 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1988_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1989 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1990_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  text-align:right;
}
#u1990 {
  border-width:0px;
  position:absolute;
  left:909px;
  top:660px;
  width:47px;
  height:11px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  text-align:right;
}
#u1990 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1990_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1991_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(0, 121, 254, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#FFFFFF;
  text-align:center;
  line-height:20px;
}
#u1991 {
  border-width:0px;
  position:absolute;
  left:1034px;
  top:651px;
  width:28px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#FFFFFF;
  text-align:center;
  line-height:20px;
}
#u1991 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1991_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1992_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0079FE;
  text-align:center;
  line-height:20px;
}
#u1992 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:651px;
  width:28px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0079FE;
  text-align:center;
  line-height:20px;
}
#u1992 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1992_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1993_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0079FE;
  text-align:center;
  line-height:20px;
}
#u1993 {
  border-width:0px;
  position:absolute;
  left:1133px;
  top:651px;
  width:28px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0079FE;
  text-align:center;
  line-height:20px;
}
#u1993 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1993_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1994_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0079FE;
  text-align:center;
  line-height:20px;
}
#u1994 {
  border-width:0px;
  position:absolute;
  left:1166px;
  top:651px;
  width:28px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0079FE;
  text-align:center;
  line-height:20px;
}
#u1994 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1994_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1995_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0079FE;
  text-align:center;
  line-height:20px;
}
#u1995 {
  border-width:0px;
  position:absolute;
  left:1067px;
  top:651px;
  width:28px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0079FE;
  text-align:center;
  line-height:20px;
}
#u1995 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1995_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1996_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#0079FE;
  text-align:center;
  line-height:20px;
}
#u1996 {
  border-width:0px;
  position:absolute;
  left:1001px;
  top:651px;
  width:28px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#0079FE;
  text-align:center;
  line-height:20px;
}
#u1996 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1996_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1997_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#145CCD;
  text-align:center;
  line-height:20px;
}
#u1997 {
  border-width:0px;
  position:absolute;
  left:1232px;
  top:651px;
  width:28px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#145CCD;
  text-align:center;
  line-height:20px;
}
#u1997 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1997_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1998_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0079FE;
  text-align:center;
  line-height:20px;
}
#u1998 {
  border-width:0px;
  position:absolute;
  left:1199px;
  top:651px;
  width:28px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0079FE;
  text-align:center;
  line-height:20px;
}
#u1998 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1998_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1999 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2000_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
}
#u2000 {
  border-width:0px;
  position:absolute;
  left:1332px;
  top:651px;
  width:28px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
}
#u2000 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2000_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(20, 92, 199, 1);
  border-radius:4px;
  -moz-box-shadow:0px 0px 6px rgba(20, 92, 199, 0.4980392156862745);
  -webkit-box-shadow:0px 0px 6px rgba(20, 92, 199, 0.4980392156862745);
  box-shadow:0px 0px 6px rgba(20, 92, 199, 0.4980392156862745);
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
}
#u2000.mouseOver {
}
#u2000_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(229, 229, 229, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
}
#u2000.disabled {
}
#u2000_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2001_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u2001 {
  border-width:0px;
  position:absolute;
  left:1305px;
  top:660px;
  width:22px;
  height:11px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u2001 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2001_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2002_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u2002 {
  border-width:0px;
  position:absolute;
  left:1365px;
  top:660px;
  width:11px;
  height:11px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u2002 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2002_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2003_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0079FE;
  text-align:center;
  line-height:20px;
}
#u2003 {
  border-width:0px;
  position:absolute;
  left:966px;
  top:651px;
  width:30px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0079FE;
  text-align:center;
  line-height:20px;
}
#u2003 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2003_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2004_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0079FE;
  text-align:center;
  line-height:20px;
}
#u2004 {
  border-width:0px;
  position:absolute;
  left:1265px;
  top:651px;
  width:30px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#0079FE;
  text-align:center;
  line-height:20px;
}
#u2004 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2004_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2005_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2005 {
  border-width:0px;
  position:absolute;
  left:526px;
  top:225px;
  width:96px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2005 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2005_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2006_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u2006 {
  border-width:0px;
  position:absolute;
  left:1361px;
  top:221px;
  width:25px;
  height:25px;
  display:flex;
}
#u2006 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2006_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2007 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2008_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:203px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
  text-align:left;
}
#u2008 {
  border-width:0px;
  position:absolute;
  left:606px;
  top:281px;
  width:203px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
  text-align:left;
}
#u2008 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2008_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2009_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:7px;
}
#u2009 {
  border-width:0px;
  position:absolute;
  left:782px;
  top:296px;
  width:12px;
  height:7px;
  display:flex;
}
#u2009 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2009_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2010_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u2010 {
  border-width:0px;
  position:absolute;
  left:536px;
  top:292px;
  width:70px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u2010 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2010_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2011 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2012_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
  text-align:left;
}
#u2012 {
  border-width:0px;
  position:absolute;
  left:880px;
  top:281px;
  width:210px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
  text-align:left;
}
#u2012 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2012_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2013_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u2013 {
  border-width:0px;
  position:absolute;
  left:824px;
  top:292px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u2013 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2013_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2014_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:36px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u2014 {
  border-width:0px;
  position:absolute;
  left:1226px;
  top:709px;
  width:70px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u2014 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2014_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2015_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:36px;
  background:inherit;
  background-color:rgba(0, 121, 254, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u2015 {
  border-width:0px;
  position:absolute;
  left:1306px;
  top:709px;
  width:70px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u2015 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2015_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2016_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:36px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u2016 {
  border-width:0px;
  position:absolute;
  left:1226px;
  top:281px;
  width:70px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u2016 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2016_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2017_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:36px;
  background:inherit;
  background-color:rgba(0, 121, 254, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u2017 {
  border-width:0px;
  position:absolute;
  left:1306px;
  top:281px;
  width:70px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u2017 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2017_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2018_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:214px;
  height:40px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.14901960784313725);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2018 {
  border-width:0px;
  position:absolute;
  left:536px;
  top:332px;
  width:214px;
  height:40px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2018 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2018_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2019_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u2019 {
  border-width:0px;
  position:absolute;
  left:551px;
  top:345px;
  width:110px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u2019 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2019_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2020_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u2020 {
  border-width:0px;
  position:absolute;
  left:721px;
  top:345px;
  width:14px;
  height:14px;
  display:flex;
}
#u2020 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2020_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2021_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:214px;
  height:40px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.14901960784313725);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2021 {
  border-width:0px;
  position:absolute;
  left:760px;
  top:332px;
  width:214px;
  height:40px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2021 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2021_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2022_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u2022 {
  border-width:0px;
  position:absolute;
  left:775px;
  top:345px;
  width:138px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u2022 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2022_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2023_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u2023 {
  border-width:0px;
  position:absolute;
  left:945px;
  top:345px;
  width:14px;
  height:14px;
  display:flex;
}
#u2023 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2023_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2024_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2024 {
  border-width:0px;
  position:absolute;
  left:545px;
  top:430px;
  width:16px;
  height:16px;
  display:flex;
}
#u2024 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2024_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2025_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2025 {
  border-width:0px;
  position:absolute;
  left:545px;
  top:464px;
  width:16px;
  height:16px;
  display:flex;
}
#u2025 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2025_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2026_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2026 {
  border-width:0px;
  position:absolute;
  left:545px;
  top:498px;
  width:16px;
  height:16px;
  display:flex;
}
#u2026 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2026_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2027_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2027 {
  border-width:0px;
  position:absolute;
  left:545px;
  top:532px;
  width:16px;
  height:16px;
  display:flex;
}
#u2027 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2027_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2028_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2028 {
  border-width:0px;
  position:absolute;
  left:545px;
  top:566px;
  width:16px;
  height:16px;
  display:flex;
}
#u2028 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2028_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2029_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
  text-align:center;
}
#u2029 {
  border-width:0px;
  position:absolute;
  left:531px;
  top:167px;
  width:32px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
  text-align:center;
}
#u2029 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2029_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2030_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
  text-align:center;
}
#u2030 {
  border-width:0px;
  position:absolute;
  left:998px;
  top:167px;
  width:32px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
  text-align:center;
}
#u2030 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2030_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
