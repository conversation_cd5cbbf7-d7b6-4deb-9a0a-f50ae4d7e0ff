﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="520px" height="100px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="1402px" y="859px" width="520px" height="100px" filterUnits="userSpaceOnUse" id="filter37">
      <feOffset dx="0" dy="-5" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0.6666666666666666  0 0 0 0 0.6666666666666666  0 0 0 0 0.6666666666666666  0 0 0 0.34901960784313724 0  " in="shadowComposite" />
    </filter>
    <g id="widget38">
      <path d="M 1412 874  L 1912 874  L 1912 954  L 1417 954  A 5 5 0 0 1 1412 949 L 1412 874  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" fill-opacity="0.9921568627450981" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -1402 -859 )">
    <use xlink:href="#widget38" filter="url(#filter37)" />
    <use xlink:href="#widget38" />
  </g>
</svg>