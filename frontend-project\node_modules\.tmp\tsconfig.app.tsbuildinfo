{"root": ["../../env.d.ts", "../../src/app.vue", "../../src/approot.vue", "../../src/main.ts", "../../src/micro-app.ts", "../../src/components/echartsdemo.vue", "../../src/components/helloworld.vue", "../../src/components/thewelcome.vue", "../../src/components/vxetabledemo.vue", "../../src/components/welcomeitem.vue", "../../src/components/icons/iconcommunity.vue", "../../src/components/icons/icondocumentation.vue", "../../src/components/icons/iconecosystem.vue", "../../src/components/icons/iconsupport.vue", "../../src/components/icons/icontooling.vue", "../../src/router/index.ts", "../../src/stores/index.ts", "../../src/types/index.ts", "../../src/views/dashboard.vue", "../../src/views/loginpage.vue"], "errors": true, "version": "5.8.3"}