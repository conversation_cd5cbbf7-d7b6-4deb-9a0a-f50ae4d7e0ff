﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:2275px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u597_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:1418px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u597 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:1418px;
  display:flex;
}
#u597 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u597_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u599_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:884px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:10px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  -webkit-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
}
#u599 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:200px;
  height:884px;
  display:flex;
}
#u599 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u599_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u600_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u600 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u600 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u600_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u601_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:70px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  -webkit-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
}
#u601 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:70px;
  display:flex;
}
#u601 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u601_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u602 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u603_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u603 {
  border-width:0px;
  position:absolute;
  left:1840px;
  top:20px;
  width:30px;
  height:30px;
  display:flex;
}
#u603 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u603_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u604_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:4px;
}
#u604 {
  border-width:0px;
  position:absolute;
  left:1875px;
  top:33px;
  width:7px;
  height:4px;
  display:flex;
  color:#555555;
}
#u604 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u604_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u605 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u606_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u606 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:26px;
  width:73px;
  height:19px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u606 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u606_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u607_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u607 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:20px;
  width:30px;
  height:30px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u607 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u607_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u608 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u609_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:153px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:0px 0px 10px rgba(85, 85, 85, 0.34901960784313724);
  -webkit-box-shadow:0px 0px 10px rgba(85, 85, 85, 0.34901960784313724);
  box-shadow:0px 0px 10px rgba(85, 85, 85, 0.34901960784313724);
}
#u609 {
  border-width:0px;
  position:absolute;
  left:1762px;
  top:59px;
  width:150px;
  height:153px;
  display:flex;
}
#u609 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u609_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u610_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:center;
}
#u610 {
  border-width:0px;
  position:absolute;
  left:1816px;
  top:79px;
  width:42px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:center;
}
#u610 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u610_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u611_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u611 {
  border-width:0px;
  position:absolute;
  left:1809px;
  top:134px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u611 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u611_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u612_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u612 {
  border-width:0px;
  position:absolute;
  left:1809px;
  top:178px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u612 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u612_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u613_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:2px;
}
#u613 {
  border-width:0px;
  position:absolute;
  left:1772px;
  top:113px;
  width:130px;
  height:1px;
  display:flex;
}
#u613 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u613_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u614_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u614 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:122px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u614 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u614_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u615_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u615 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:174px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u615 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u615_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u616_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u616 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:226px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u616 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u616_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u617_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u617 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:88px;
  width:70px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u617 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u617_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u618_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u618 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:140px;
  width:84px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u618 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u618_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u619_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u619 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:192px;
  width:129px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u619 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u619_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u620_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u620 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:244px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u620 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u620_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u621_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u621 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:241px;
  width:20px;
  height:20px;
  display:flex;
}
#u621 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u621_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u622_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u622 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:85px;
  width:20px;
  height:20px;
  display:flex;
}
#u622 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u622_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u623_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u623 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:137px;
  width:20px;
  height:20px;
  display:flex;
}
#u623 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u623_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u624_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u624 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:189px;
  width:20px;
  height:20px;
  display:flex;
}
#u624 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u624_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u625_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1692px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u625 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:80px;
  width:1692px;
  height:60px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u625 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u625_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u626_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u626 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:102px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u626 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u626_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u627_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:3px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u627 {
  border-width:0px;
  position:absolute;
  left:882px;
  top:137px;
  width:60px;
  height:3px;
  display:flex;
}
#u627 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u627_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u628_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u628 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:102px;
  width:96px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u628 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u628_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u629_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u629 {
  border-width:0px;
  position:absolute;
  left:676px;
  top:102px;
  width:128px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u629 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u629_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u630_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
}
#u630 {
  border-width:0px;
  position:absolute;
  left:864px;
  top:102px;
  width:96px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
}
#u630 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u630_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u631_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u631 {
  border-width:0px;
  position:absolute;
  left:1020px;
  top:102px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u631 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u631_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u632_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u632 {
  border-width:0px;
  position:absolute;
  left:520px;
  top:102px;
  width:96px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u632 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u632_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u633_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:41px;
}
#u633 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:90px;
  width:1px;
  height:40px;
  display:flex;
}
#u633 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u633_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u634_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1692px;
  height:525px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u634 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:150px;
  width:1692px;
  height:525px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u634 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u634_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u635_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u635 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:165px;
  width:96px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u635 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u635_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u636_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:826px;
  height:450px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u636 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:210px;
  width:826px;
  height:450px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u636 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u636_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u637_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u637 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:225px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u637 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u637_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u638 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u639_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:727px;
  height:2px;
}
#u639 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:598px;
  width:726px;
  height:1px;
  display:flex;
}
#u639 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u639_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u640_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:727px;
  height:2px;
}
#u640 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:474px;
  width:726px;
  height:1px;
  display:flex;
}
#u640 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u640_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u641_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:727px;
  height:2px;
}
#u641 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:412px;
  width:726px;
  height:1px;
  display:flex;
}
#u641 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u641_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u642_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:727px;
  height:2px;
}
#u642 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:350px;
  width:726px;
  height:1px;
  display:flex;
}
#u642 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u642_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u643_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:727px;
  height:2px;
}
#u643 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:288px;
  width:726px;
  height:1px;
  display:flex;
}
#u643 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u643_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u644_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u644 {
  border-width:0px;
  position:absolute;
  left:253px;
  top:589px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u644 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u644_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u645_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u645 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:403px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u645 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u645_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u646_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u646 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:279px;
  width:20px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u646 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u646_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u647_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u647 {
  border-width:0px;
  position:absolute;
  left:294px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u647 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u647_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u648_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u648 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:609px;
  width:48px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u648 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u648_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u649_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u649 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:465px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u649 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u649_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u650_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u650 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:341px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u650 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u650_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u651_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:727px;
  height:2px;
}
#u651 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:536px;
  width:726px;
  height:1px;
  display:flex;
}
#u651 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u651_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u652_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u652 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:527px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u652 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u652_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u653_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u653 {
  border-width:0px;
  position:absolute;
  left:369px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u653 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u653_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u654_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u654 {
  border-width:0px;
  position:absolute;
  left:346px;
  top:609px;
  width:48px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u654 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u654_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u655_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u655 {
  border-width:0px;
  position:absolute;
  left:445px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u655 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u655_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u656_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u656 {
  border-width:0px;
  position:absolute;
  left:422px;
  top:609px;
  width:48px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u656 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u656_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u657_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u657 {
  border-width:0px;
  position:absolute;
  left:520px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u657 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u657_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u658_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u658 {
  border-width:0px;
  position:absolute;
  left:497px;
  top:609px;
  width:48px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u658 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u658_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u659_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u659 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:249px;
  width:24px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u659 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u659_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u660_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u660 {
  border-width:0px;
  position:absolute;
  left:1006px;
  top:589px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u660 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u660_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u661_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u661 {
  border-width:0px;
  position:absolute;
  left:595px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u661 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u661_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u662_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u662 {
  border-width:0px;
  position:absolute;
  left:572px;
  top:609px;
  width:48px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u662 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u662_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u663_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u663 {
  border-width:0px;
  position:absolute;
  left:1006px;
  top:403px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u663 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u663_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u664_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u664 {
  border-width:0px;
  position:absolute;
  left:1006px;
  top:279px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u664 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u664_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u665_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u665 {
  border-width:0px;
  position:absolute;
  left:1006px;
  top:465px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u665 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u665_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u666_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u666 {
  border-width:0px;
  position:absolute;
  left:1006px;
  top:341px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u666 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u666_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u667_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u667 {
  border-width:0px;
  position:absolute;
  left:1006px;
  top:527px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u667 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u667_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u668_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u668 {
  border-width:0px;
  position:absolute;
  left:1000px;
  top:249px;
  width:36px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u668 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u668_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u669_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u669 {
  border-width:0px;
  position:absolute;
  left:671px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u669 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u669_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u670_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u670 {
  border-width:0px;
  position:absolute;
  left:648px;
  top:609px;
  width:48px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u670 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u670_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u671_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u671 {
  border-width:0px;
  position:absolute;
  left:746px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u671 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u671_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u672_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u672 {
  border-width:0px;
  position:absolute;
  left:723px;
  top:609px;
  width:48px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u672 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u672_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u673_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u673 {
  border-width:0px;
  position:absolute;
  left:821px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u673 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u673_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u674_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u674 {
  border-width:0px;
  position:absolute;
  left:798px;
  top:609px;
  width:48px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u674 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u674_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u675_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u675 {
  border-width:0px;
  position:absolute;
  left:897px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u675 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u675_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u676_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:261px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u676 {
  border-width:0px;
  position:absolute;
  left:284px;
  top:337px;
  width:21px;
  height:261px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u676 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u676_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u677_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:175px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u677 {
  border-width:0px;
  position:absolute;
  left:359px;
  top:423px;
  width:21px;
  height:175px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u677 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u677_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u678_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:99px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u678 {
  border-width:0px;
  position:absolute;
  left:435px;
  top:499px;
  width:21px;
  height:99px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u678 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u678_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u679_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:206px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u679 {
  border-width:0px;
  position:absolute;
  left:510px;
  top:392px;
  width:21px;
  height:206px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u679 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u679_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u680_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:171px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u680 {
  border-width:0px;
  position:absolute;
  left:585px;
  top:427px;
  width:21px;
  height:171px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u680 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u680_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u681_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:223px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u681 {
  border-width:0px;
  position:absolute;
  left:661px;
  top:375px;
  width:21px;
  height:223px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u681 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u681_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u682_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:289px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u682 {
  border-width:0px;
  position:absolute;
  left:736px;
  top:309px;
  width:21px;
  height:289px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u682 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u682_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u683_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:77px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u683 {
  border-width:0px;
  position:absolute;
  left:811px;
  top:521px;
  width:21px;
  height:77px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u683 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u683_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u684 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u685_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:681px;
  height:89px;
}
#u685p000 {
  border-width:0px;
  position:absolute;
  left:-4px;
  top:19px;
  width:84px;
  height:6px;
  -webkit-transform:rotate(12deg);
  -moz-transform:rotate(12deg);
  -ms-transform:rotate(12deg);
  transform:rotate(12deg);
}
#u685p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:6px;
}
#u685p001 {
  border-width:0px;
  position:absolute;
  left:72px;
  top:21px;
  width:84px;
  height:6px;
  -webkit-transform:rotate(-10deg);
  -moz-transform:rotate(-10deg);
  -ms-transform:rotate(-10deg);
  transform:rotate(-10deg);
}
#u685p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:6px;
}
#u685p002 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:49px;
  width:110px;
  height:6px;
  -webkit-transform:rotate(44deg);
  -moz-transform:rotate(44deg);
  -ms-transform:rotate(44deg);
  transform:rotate(44deg);
}
#u685p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:6px;
}
#u685p003 {
  border-width:0px;
  position:absolute;
  left:222px;
  top:71px;
  width:84px;
  height:6px;
  -webkit-transform:rotate(-19deg);
  -moz-transform:rotate(-19deg);
  -ms-transform:rotate(-19deg);
  transform:rotate(-19deg);
}
#u685p003_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:6px;
}
#u685p004 {
  border-width:0px;
  position:absolute;
  left:298px;
  top:61px;
  width:82px;
  height:6px;
  -webkit-transform:rotate(5deg);
  -moz-transform:rotate(5deg);
  -ms-transform:rotate(5deg);
  transform:rotate(5deg);
}
#u685p004_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:6px;
}
#u685p005 {
  border-width:0px;
  position:absolute;
  left:362px;
  top:31px;
  width:106px;
  height:6px;
  -webkit-transform:rotate(-42deg);
  -moz-transform:rotate(-42deg);
  -ms-transform:rotate(-42deg);
  transform:rotate(-42deg);
}
#u685p005_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:6px;
}
#u685p006 {
  border-width:0px;
  position:absolute;
  left:438px;
  top:31px;
  width:106px;
  height:6px;
  -webkit-transform:rotate(42deg);
  -moz-transform:rotate(42deg);
  -ms-transform:rotate(42deg);
  transform:rotate(42deg);
}
#u685p006_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:6px;
}
#u685p007 {
  border-width:0px;
  position:absolute;
  left:524px;
  top:59px;
  width:82px;
  height:6px;
  -webkit-transform:rotate(-7deg);
  -moz-transform:rotate(-7deg);
  -ms-transform:rotate(-7deg);
  transform:rotate(-7deg);
}
#u685p007_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:6px;
}
#u685p008 {
  border-width:0px;
  position:absolute;
  left:598px;
  top:71px;
  width:86px;
  height:6px;
  -webkit-transform:rotate(21deg);
  -moz-transform:rotate(21deg);
  -ms-transform:rotate(21deg);
  transform:rotate(21deg);
}
#u685p008_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:6px;
}
#u685.compound {
  width:0px;
  height:0px;
}
#u685 {
  border-width:0px;
  position:absolute;
  left:294px;
  top:369px;
  width:680px;
  height:88px;
  display:flex;
}
#u685 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u685_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u686_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:5px;
}
#u686 {
  border-width:0px;
  position:absolute;
  left:292px;
  top:381px;
  width:5px;
  height:5px;
  display:flex;
}
#u686 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u686_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u687_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:5px;
}
#u687 {
  border-width:0px;
  position:absolute;
  left:443px;
  top:383px;
  width:5px;
  height:5px;
  display:flex;
}
#u687 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u687_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u688_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:5px;
}
#u688 {
  border-width:0px;
  position:absolute;
  left:518px;
  top:454px;
  width:5px;
  height:5px;
  display:flex;
}
#u688 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u688_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u689_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:5px;
}
#u689 {
  border-width:0px;
  position:absolute;
  left:593px;
  top:428px;
  width:5px;
  height:5px;
  display:flex;
}
#u689 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u689_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u690_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:5px;
}
#u690 {
  border-width:0px;
  position:absolute;
  left:669px;
  top:434px;
  width:5px;
  height:5px;
  display:flex;
}
#u690 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u690_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u691_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:5px;
}
#u691 {
  border-width:0px;
  position:absolute;
  left:744px;
  top:367px;
  width:5px;
  height:5px;
  display:flex;
}
#u691 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u691_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u692_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:5px;
}
#u692 {
  border-width:0px;
  position:absolute;
  left:819px;
  top:434px;
  width:5px;
  height:5px;
  display:flex;
}
#u692 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u692_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u693_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:5px;
}
#u693 {
  border-width:0px;
  position:absolute;
  left:895px;
  top:425px;
  width:5px;
  height:5px;
  display:flex;
}
#u693 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u693_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u694_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:5px;
}
#u694 {
  border-width:0px;
  position:absolute;
  left:970px;
  top:454px;
  width:5px;
  height:5px;
  display:flex;
}
#u694 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u694_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u695_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:99px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u695 {
  border-width:0px;
  position:absolute;
  left:887px;
  top:499px;
  width:21px;
  height:99px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u695 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u695_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u696_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:138px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u696 {
  border-width:0px;
  position:absolute;
  left:962px;
  top:460px;
  width:21px;
  height:138px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u696 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u696_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u697_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:5px;
}
#u697 {
  border-width:0px;
  position:absolute;
  left:367px;
  top:342px;
  width:5px;
  height:5px;
  display:flex;
}
#u697 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u697_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u698 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u699 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u700 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u701_img {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-10px;
  width:141px;
  height:79px;
}
#u701 {
  border-width:0px;
  position:absolute;
  left:310px;
  top:337px;
  width:121px;
  height:59px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u701 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u701_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u702_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u702 {
  border-width:0px;
  position:absolute;
  left:320px;
  top:364px;
  width:35px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u702 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u702_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u703_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u703 {
  border-width:0px;
  position:absolute;
  left:320px;
  top:347px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u703 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u703_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u704_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u704 {
  border-width:0px;
  position:absolute;
  left:325px;
  top:372px;
  width:6px;
  height:6px;
  display:flex;
}
#u704 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u704_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u705_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  text-align:right;
  line-height:12px;
}
#u705 {
  border-width:0px;
  position:absolute;
  left:336px;
  top:369px;
  width:14px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  text-align:right;
  line-height:12px;
}
#u705 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u705_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u706_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u706 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:364px;
  width:61px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u706 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u706_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u707_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u707 {
  border-width:0px;
  position:absolute;
  left:365px;
  top:372px;
  width:6px;
  height:6px;
  display:flex;
}
#u707 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u707_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u708_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#2FC25B;
  text-align:right;
  line-height:12px;
}
#u708 {
  border-width:0px;
  position:absolute;
  left:376px;
  top:369px;
  width:40px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#2FC25B;
  text-align:right;
  line-height:12px;
}
#u708 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u708_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u709_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u709 {
  border-width:0px;
  position:absolute;
  left:874px;
  top:609px;
  width:48px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u709 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u709_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u710_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u710 {
  border-width:0px;
  position:absolute;
  left:972px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u710 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u710_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u711_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u711 {
  border-width:0px;
  position:absolute;
  left:949px;
  top:609px;
  width:48px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u711 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u711_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u712_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:826px;
  height:450px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u712 {
  border-width:0px;
  position:absolute;
  left:1061px;
  top:210px;
  width:826px;
  height:450px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u712 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u712_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u713_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:5px;
  height:23px;
}
#u713 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:163px;
  width:2px;
  height:20px;
  display:flex;
}
#u713 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u713_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u714_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u714 {
  border-width:0px;
  position:absolute;
  left:1076px;
  top:225px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u714 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u714_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u715 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u716_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:749px;
  height:2px;
}
#u716 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:598px;
  width:748px;
  height:1px;
  display:flex;
}
#u716 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u716_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u717_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:749px;
  height:2px;
}
#u717 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:474px;
  width:748px;
  height:1px;
  display:flex;
}
#u717 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u717_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u718_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:749px;
  height:2px;
}
#u718 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:412px;
  width:748px;
  height:1px;
  display:flex;
}
#u718 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u718_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u719_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:749px;
  height:2px;
}
#u719 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:350px;
  width:748px;
  height:1px;
  display:flex;
}
#u719 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u719_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u720_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:749px;
  height:2px;
}
#u720 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:288px;
  width:748px;
  height:1px;
  display:flex;
}
#u720 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u720_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u721_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u721 {
  border-width:0px;
  position:absolute;
  left:1083px;
  top:589px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u721 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u721_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u722_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u722 {
  border-width:0px;
  position:absolute;
  left:1083px;
  top:403px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u722 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u722_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u723_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u723 {
  border-width:0px;
  position:absolute;
  left:1076px;
  top:279px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u723 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u723_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u724_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u724 {
  border-width:0px;
  position:absolute;
  left:1124px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u724 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u724_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u725_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u725 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:609px;
  width:48px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u725 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u725_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u726_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u726 {
  border-width:0px;
  position:absolute;
  left:1083px;
  top:465px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u726 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u726_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u727_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u727 {
  border-width:0px;
  position:absolute;
  left:1083px;
  top:341px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u727 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u727_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u728_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:749px;
  height:2px;
}
#u728 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:536px;
  width:748px;
  height:1px;
  display:flex;
}
#u728 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u728_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u729_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u729 {
  border-width:0px;
  position:absolute;
  left:1083px;
  top:527px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u729 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u729_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u730_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u730 {
  border-width:0px;
  position:absolute;
  left:1199px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u730 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u730_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u731_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u731 {
  border-width:0px;
  position:absolute;
  left:1176px;
  top:609px;
  width:48px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u731 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u731_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u732_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u732 {
  border-width:0px;
  position:absolute;
  left:1275px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u732 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u732_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u733_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u733 {
  border-width:0px;
  position:absolute;
  left:1252px;
  top:609px;
  width:48px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u733 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u733_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u734_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u734 {
  border-width:0px;
  position:absolute;
  left:1350px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u734 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u734_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u735_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u735 {
  border-width:0px;
  position:absolute;
  left:1327px;
  top:609px;
  width:48px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u735 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u735_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u736_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u736 {
  border-width:0px;
  position:absolute;
  left:1076px;
  top:249px;
  width:80px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u736 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u736_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u737_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u737 {
  border-width:0px;
  position:absolute;
  left:1858px;
  top:589px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u737 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u737_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u738_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u738 {
  border-width:0px;
  position:absolute;
  left:1425px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u738 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u738_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u739_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u739 {
  border-width:0px;
  position:absolute;
  left:1402px;
  top:609px;
  width:48px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u739 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u739_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u740_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u740 {
  border-width:0px;
  position:absolute;
  left:1858px;
  top:403px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u740 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u740_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u741_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u741 {
  border-width:0px;
  position:absolute;
  left:1858px;
  top:279px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u741 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u741_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u742_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u742 {
  border-width:0px;
  position:absolute;
  left:1858px;
  top:465px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u742 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u742_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u743_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u743 {
  border-width:0px;
  position:absolute;
  left:1858px;
  top:341px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u743 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u743_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u744_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u744 {
  border-width:0px;
  position:absolute;
  left:1858px;
  top:527px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u744 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u744_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u745_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u745 {
  border-width:0px;
  position:absolute;
  left:1797px;
  top:249px;
  width:75px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u745 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u745_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u746_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u746 {
  border-width:0px;
  position:absolute;
  left:1501px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u746 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u746_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u747_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u747 {
  border-width:0px;
  position:absolute;
  left:1478px;
  top:609px;
  width:48px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u747 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u747_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u748_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u748 {
  border-width:0px;
  position:absolute;
  left:1576px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u748 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u748_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u749_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u749 {
  border-width:0px;
  position:absolute;
  left:1553px;
  top:609px;
  width:48px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u749 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u749_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u750_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u750 {
  border-width:0px;
  position:absolute;
  left:1651px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u750 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u750_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u751_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u751 {
  border-width:0px;
  position:absolute;
  left:1628px;
  top:609px;
  width:48px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u751 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u751_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u752_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u752 {
  border-width:0px;
  position:absolute;
  left:1727px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u752 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u752_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u753_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u753 {
  border-width:0px;
  position:absolute;
  left:1704px;
  top:609px;
  width:48px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u753 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u753_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u754_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u754 {
  border-width:0px;
  position:absolute;
  left:1802px;
  top:599px;
  width:1px;
  height:5px;
  display:flex;
}
#u754 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u754_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u755_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u755 {
  border-width:0px;
  position:absolute;
  left:1779px;
  top:609px;
  width:48px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u755 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u755_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u756_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:170px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u756 {
  border-width:0px;
  position:absolute;
  left:1109px;
  top:428px;
  width:15px;
  height:170px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u756 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u756_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u757_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:122px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u757 {
  border-width:0px;
  position:absolute;
  left:1125px;
  top:476px;
  width:15px;
  height:122px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u757 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u757_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u758_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:202px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u758 {
  border-width:0px;
  position:absolute;
  left:1184px;
  top:396px;
  width:15px;
  height:202px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u758 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u758_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u759_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:140px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u759 {
  border-width:0px;
  position:absolute;
  left:1200px;
  top:458px;
  width:15px;
  height:140px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u759 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u759_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u760_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:97px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u760 {
  border-width:0px;
  position:absolute;
  left:1260px;
  top:501px;
  width:15px;
  height:97px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u760 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u760_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u761_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:140px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u761 {
  border-width:0px;
  position:absolute;
  left:1276px;
  top:458px;
  width:15px;
  height:140px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u761 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u761_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u762_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:157px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u762 {
  border-width:0px;
  position:absolute;
  left:1335px;
  top:441px;
  width:15px;
  height:157px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u762 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u762_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u763_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:195px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u763 {
  border-width:0px;
  position:absolute;
  left:1351px;
  top:403px;
  width:15px;
  height:195px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u763 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u763_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u764_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:78px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u764 {
  border-width:0px;
  position:absolute;
  left:1410px;
  top:519px;
  width:15px;
  height:78px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u764 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u764_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u765_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:87px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u765 {
  border-width:0px;
  position:absolute;
  left:1426px;
  top:510px;
  width:15px;
  height:87px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u765 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u765_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u766_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:114px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u766 {
  border-width:0px;
  position:absolute;
  left:1486px;
  top:484px;
  width:15px;
  height:114px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u766 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u766_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u767_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:97px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u767 {
  border-width:0px;
  position:absolute;
  left:1502px;
  top:501px;
  width:15px;
  height:97px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u767 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u767_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u768_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:271px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u768 {
  border-width:0px;
  position:absolute;
  left:1561px;
  top:327px;
  width:15px;
  height:271px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u768 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u768_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u769_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:236px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u769 {
  border-width:0px;
  position:absolute;
  left:1577px;
  top:362px;
  width:15px;
  height:236px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u769 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u769_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u770_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:101px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u770 {
  border-width:0px;
  position:absolute;
  left:1636px;
  top:497px;
  width:15px;
  height:101px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u770 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u770_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u771_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:84px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u771 {
  border-width:0px;
  position:absolute;
  left:1652px;
  top:514px;
  width:15px;
  height:84px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u771 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u771_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u772_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:140px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u772 {
  border-width:0px;
  position:absolute;
  left:1712px;
  top:458px;
  width:15px;
  height:140px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u772 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u772_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u773_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:110px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u773 {
  border-width:0px;
  position:absolute;
  left:1728px;
  top:488px;
  width:15px;
  height:110px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u773 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u773_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u774_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:54px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u774 {
  border-width:0px;
  position:absolute;
  left:1787px;
  top:544px;
  width:15px;
  height:54px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u774 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u774_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u775_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:73px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u775 {
  border-width:0px;
  position:absolute;
  left:1803px;
  top:525px;
  width:15px;
  height:73px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u775 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u775_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u776 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u777 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u778 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u779_img {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-10px;
  width:222px;
  height:79px;
}
#u779 {
  border-width:0px;
  position:absolute;
  left:1371px;
  top:403px;
  width:202px;
  height:59px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u779 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u779_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u780_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:91px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u780 {
  border-width:0px;
  position:absolute;
  left:1381px;
  top:430px;
  width:91px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u780 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u780_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u781_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u781 {
  border-width:0px;
  position:absolute;
  left:1381px;
  top:413px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u781 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u781_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u782_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u782 {
  border-width:0px;
  position:absolute;
  left:1386px;
  top:438px;
  width:6px;
  height:6px;
  display:flex;
}
#u782 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u782_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u783_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u783 {
  border-width:0px;
  position:absolute;
  left:1397px;
  top:435px;
  width:70px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u783 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u783_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u784_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u784 {
  border-width:0px;
  position:absolute;
  left:1477px;
  top:430px;
  width:86px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u784 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u784_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u785_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u785 {
  border-width:0px;
  position:absolute;
  left:1482px;
  top:438px;
  width:6px;
  height:6px;
  display:flex;
}
#u785 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u785_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u786_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#2FC25B;
  text-align:right;
  line-height:12px;
}
#u786 {
  border-width:0px;
  position:absolute;
  left:1493px;
  top:435px;
  width:65px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#2FC25B;
  text-align:right;
  line-height:12px;
}
#u786 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u786_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u787_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1692px;
  height:723px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u787 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:685px;
  width:1692px;
  height:723px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u787 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u787_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u788_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:163px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u788 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:700px;
  width:163px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u788 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u788_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u789_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:5px;
  height:23px;
}
#u789 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:698px;
  width:2px;
  height:20px;
  display:flex;
}
#u789 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u789_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u790_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:826px;
  height:319px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u790 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:745px;
  width:826px;
  height:319px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u790 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u790_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u791 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u792_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:760px;
  height:2px;
}
#u792 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:965px;
  width:759px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(0.08315443810213374deg);
  -moz-transform:rotate(0.08315443810213374deg);
  -ms-transform:rotate(0.08315443810213374deg);
  transform:rotate(0.08315443810213374deg);
}
#u792 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u792_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u793_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:760px;
  height:2px;
}
#u793 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:898px;
  width:759px;
  height:1px;
  display:flex;
}
#u793 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u793_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u794_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:760px;
  height:2px;
}
#u794 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:865px;
  width:759px;
  height:1px;
  display:flex;
}
#u794 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u794_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u795_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:760px;
  height:2px;
}
#u795 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:832px;
  width:759px;
  height:1px;
  display:flex;
}
#u795 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u795_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u796_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:760px;
  height:2px;
}
#u796 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:798px;
  width:759px;
  height:1px;
  display:flex;
}
#u796 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u796_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u797_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u797 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:955px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u797 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u797_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u798_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u798 {
  border-width:0px;
  position:absolute;
  left:244px;
  top:856px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u798 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u798_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u799_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u799 {
  border-width:0px;
  position:absolute;
  left:244px;
  top:789px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u799 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u799_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u800_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u800 {
  border-width:0px;
  position:absolute;
  left:307px;
  top:965px;
  width:1px;
  height:5px;
  display:flex;
}
#u800 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u800_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u801_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u801 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:975px;
  width:62px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u801 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u801_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u802_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u802 {
  border-width:0px;
  position:absolute;
  left:251px;
  top:889px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u802 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u802_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u803_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u803 {
  border-width:0px;
  position:absolute;
  left:244px;
  top:823px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u803 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u803_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u804_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:760px;
  height:2px;
}
#u804 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:931px;
  width:759px;
  height:1px;
  display:flex;
}
#u804 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u804_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u805_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u805 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:922px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u805 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u805_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u806 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u807_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:759px;
  height:44px;
}
#u807 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:898px;
  width:758px;
  height:43px;
  display:flex;
}
#u807 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u807_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u808_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:759px;
  height:75px;
}
#u808 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:789px;
  width:758px;
  height:74px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u808 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u808_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u809_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:759px;
  height:75px;
}
#u809 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:832px;
  width:758px;
  height:74px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u809 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u809_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u810_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:760px;
  height:81px;
}
#u810 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:806px;
  width:759px;
  height:80px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u810 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u810_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u811_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:759px;
  height:75px;
}
#u811 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:837px;
  width:758px;
  height:74px;
  display:flex;
}
#u811 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u811_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u812_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:762px;
  height:80px;
}
#u812p000 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:59px;
  width:126px;
  height:12px;
  -webkit-transform:rotate(-10deg);
  -moz-transform:rotate(-10deg);
  -ms-transform:rotate(-10deg);
  transform:rotate(-10deg);
}
#u812p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  height:12px;
}
#u812p001 {
  border-width:0px;
  position:absolute;
  left:115px;
  top:51px;
  width:68px;
  height:6px;
  -webkit-transform:rotate(-3deg);
  -moz-transform:rotate(-3deg);
  -ms-transform:rotate(-3deg);
  transform:rotate(-3deg);
}
#u812p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:6px;
}
#u812p002 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:27px;
  width:202px;
  height:20px;
  -webkit-transform:rotate(-8deg);
  -moz-transform:rotate(-8deg);
  -ms-transform:rotate(-8deg);
  transform:rotate(-8deg);
}
#u812p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:20px;
}
#u812p003 {
  border-width:0px;
  position:absolute;
  left:373px;
  top:29px;
  width:78px;
  height:6px;
  -webkit-transform:rotate(8deg);
  -moz-transform:rotate(8deg);
  -ms-transform:rotate(8deg);
  transform:rotate(8deg);
}
#u812p003_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:6px;
}
#u812p004 {
  border-width:0px;
  position:absolute;
  left:445px;
  top:25px;
  width:88px;
  height:8px;
  -webkit-transform:rotate(-10deg);
  -moz-transform:rotate(-10deg);
  -ms-transform:rotate(-10deg);
  transform:rotate(-10deg);
}
#u812p004_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:88px;
  height:8px;
}
#u812p005 {
  border-width:0px;
  position:absolute;
  left:527px;
  top:15px;
  width:80px;
  height:6px;
  -webkit-transform:rotate(-7deg);
  -moz-transform:rotate(-7deg);
  -ms-transform:rotate(-7deg);
  transform:rotate(-7deg);
}
#u812p005_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:6px;
}
#u812p006 {
  border-width:0px;
  position:absolute;
  left:601px;
  top:5px;
  width:94px;
  height:6px;
  -webkit-transform:rotate(-6deg);
  -moz-transform:rotate(-6deg);
  -ms-transform:rotate(-6deg);
  transform:rotate(-6deg);
}
#u812p006_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:6px;
}
#u812p007 {
  border-width:0px;
  position:absolute;
  left:691px;
  top:1px;
  width:70px;
  height:4px;
  -webkit-transform:rotate(-3deg);
  -moz-transform:rotate(-3deg);
  -ms-transform:rotate(-3deg);
  transform:rotate(-3deg);
}
#u812p007_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:4px;
}
#u812.compound {
  width:0px;
  height:0px;
}
#u812 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:829px;
  width:759px;
  height:77px;
  display:flex;
}
#u812 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u812_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u813_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:762px;
  height:83px;
}
#u813 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:826px;
  width:759px;
  height:80px;
  display:flex;
}
#u813 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u813_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u814_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:761px;
  height:123px;
}
#u814 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:806px;
  width:758px;
  height:120px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u814 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u814_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u815_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:168px;
}
#u815 {
  border-width:0px;
  position:absolute;
  left:656px;
  top:798px;
  width:1px;
  height:167px;
  display:flex;
}
#u815 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u815_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u816 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u817_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u817 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:852px;
  width:9px;
  height:9px;
  display:flex;
  color:transparent;
}
#u817 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u817_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u818 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u819 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u820 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u821_img {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-10px;
  width:159px;
  height:79px;
}
#u821 {
  border-width:0px;
  position:absolute;
  left:666px;
  top:841px;
  width:139px;
  height:59px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u821 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u821_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u822_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u822 {
  border-width:0px;
  position:absolute;
  left:676px;
  top:868px;
  width:119px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u822 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u822_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u823_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u823 {
  border-width:0px;
  position:absolute;
  left:676px;
  top:851px;
  width:62px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u823 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u823_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u824_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u824 {
  border-width:0px;
  position:absolute;
  left:681px;
  top:876px;
  width:6px;
  height:6px;
  display:flex;
}
#u824 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u824_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u825_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  text-align:right;
  line-height:12px;
}
#u825 {
  border-width:0px;
  position:absolute;
  left:750px;
  top:873px;
  width:40px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  text-align:right;
  line-height:12px;
}
#u825 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u825_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u826_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u826 {
  border-width:0px;
  position:absolute;
  left:692px;
  top:873px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u826 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u826_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u827_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u827 {
  border-width:0px;
  position:absolute;
  left:423px;
  top:965px;
  width:1px;
  height:5px;
  display:flex;
}
#u827 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u827_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u828_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u828 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:975px;
  width:62px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u828 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u828_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u829_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u829 {
  border-width:0px;
  position:absolute;
  left:539px;
  top:965px;
  width:1px;
  height:5px;
  display:flex;
}
#u829 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u829_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u830_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u830 {
  border-width:0px;
  position:absolute;
  left:509px;
  top:975px;
  width:62px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u830 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u830_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u831_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u831 {
  border-width:0px;
  position:absolute;
  left:656px;
  top:965px;
  width:1px;
  height:5px;
  display:flex;
}
#u831 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u831_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u832_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u832 {
  border-width:0px;
  position:absolute;
  left:626px;
  top:975px;
  width:62px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u832 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u832_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u833_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u833 {
  border-width:0px;
  position:absolute;
  left:772px;
  top:965px;
  width:1px;
  height:5px;
  display:flex;
}
#u833 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u833_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u834_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u834 {
  border-width:0px;
  position:absolute;
  left:742px;
  top:975px;
  width:62px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u834 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u834_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u835_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u835 {
  border-width:0px;
  position:absolute;
  left:888px;
  top:965px;
  width:1px;
  height:5px;
  display:flex;
}
#u835 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u835_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u836_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u836 {
  border-width:0px;
  position:absolute;
  left:858px;
  top:975px;
  width:62px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u836 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u836_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u837_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u837 {
  border-width:0px;
  position:absolute;
  left:1004px;
  top:965px;
  width:1px;
  height:5px;
  display:flex;
}
#u837 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u837_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u838_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u838 {
  border-width:0px;
  position:absolute;
  left:974px;
  top:975px;
  width:62px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u838 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u838_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u839_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u839 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:760px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u839 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u839_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u840_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
  text-align:left;
}
#u840 {
  border-width:0px;
  position:absolute;
  left:798px;
  top:755px;
  width:105px;
  height:24px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
  text-align:left;
}
#u840 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 10px;
  box-sizing:border-box;
  width:100%;
}
#u840_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u841_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u841 {
  border-width:0px;
  position:absolute;
  left:913px;
  top:761px;
  width:8px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:center;
}
#u841 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u841_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u842_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
  text-align:left;
}
#u842 {
  border-width:0px;
  position:absolute;
  left:931px;
  top:755px;
  width:105px;
  height:24px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
  text-align:left;
}
#u842 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 10px;
  box-sizing:border-box;
  width:100%;
}
#u842_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u843_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u843 {
  border-width:0px;
  position:absolute;
  left:884px;
  top:760px;
  width:14px;
  height:14px;
  display:flex;
}
#u843 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u843_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u844_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u844 {
  border-width:0px;
  position:absolute;
  left:1017px;
  top:760px;
  width:14px;
  height:14px;
  display:flex;
}
#u844 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u844_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u845_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u845 {
  border-width:0px;
  position:absolute;
  left:357px;
  top:1016px;
  width:10px;
  height:10px;
  display:flex;
}
#u845 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u845_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u846_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u846 {
  border-width:0px;
  position:absolute;
  left:372px;
  top:1015px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u846 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u846_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u847_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u847 {
  border-width:0px;
  position:absolute;
  left:773px;
  top:1016px;
  width:10px;
  height:10px;
  display:flex;
}
#u847 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u847_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u848_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u848 {
  border-width:0px;
  position:absolute;
  left:788px;
  top:1015px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u848 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u848_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u849_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u849 {
  border-width:0px;
  position:absolute;
  left:440px;
  top:1016px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u849 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u849_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u850_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u850 {
  border-width:0px;
  position:absolute;
  left:455px;
  top:1015px;
  width:84px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u850 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u850_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u851_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u851 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:1016px;
  width:10px;
  height:10px;
  display:flex;
}
#u851 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u851_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u852_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u852 {
  border-width:0px;
  position:absolute;
  left:574px;
  top:1015px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u852 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u852_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u853_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u853 {
  border-width:0px;
  position:absolute;
  left:666px;
  top:1016px;
  width:10px;
  height:10px;
  display:flex;
}
#u853 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u853_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u854_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u854 {
  border-width:0px;
  position:absolute;
  left:681px;
  top:1015px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u854 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u854_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u855_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u855 {
  border-width:0px;
  position:absolute;
  left:856px;
  top:1017px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u855 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u855_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u856_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u856 {
  border-width:0px;
  position:absolute;
  left:871px;
  top:1016px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u856 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u856_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u857_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u857 {
  border-width:0px;
  position:absolute;
  left:440px;
  top:1038px;
  width:10px;
  height:10px;
  display:flex;
}
#u857 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u857_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u858_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u858 {
  border-width:0px;
  position:absolute;
  left:455px;
  top:1037px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u858 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u858_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u859_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u859 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:1038px;
  width:10px;
  height:10px;
  display:flex;
}
#u859 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u859_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u860_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u860 {
  border-width:0px;
  position:absolute;
  left:574px;
  top:1037px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u860 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u860_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u861_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(19, 190, 180, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u861 {
  border-width:0px;
  position:absolute;
  left:666px;
  top:1038px;
  width:10px;
  height:10px;
  display:flex;
}
#u861 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u861_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u862_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u862 {
  border-width:0px;
  position:absolute;
  left:681px;
  top:1037px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u862 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u862_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u863_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u863 {
  border-width:0px;
  position:absolute;
  left:773px;
  top:1038px;
  width:10px;
  height:10px;
  display:flex;
}
#u863 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u863_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u864_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u864 {
  border-width:0px;
  position:absolute;
  left:788px;
  top:1037px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u864 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u864_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u865_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:826px;
  height:319px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u865 {
  border-width:0px;
  position:absolute;
  left:1061px;
  top:745px;
  width:826px;
  height:319px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u865 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u865_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u866 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u867_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:764px;
  height:2px;
}
#u867 {
  border-width:0px;
  position:absolute;
  left:1109px;
  top:920px;
  width:763px;
  height:1px;
  display:flex;
}
#u867 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u867_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u868_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:764px;
  height:2px;
}
#u868 {
  border-width:0px;
  position:absolute;
  left:1109px;
  top:880px;
  width:763px;
  height:1px;
  display:flex;
}
#u868 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u868_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u869_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:764px;
  height:2px;
}
#u869 {
  border-width:0px;
  position:absolute;
  left:1109px;
  top:839px;
  width:763px;
  height:1px;
  display:flex;
}
#u869 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u869_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u870_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:764px;
  height:2px;
}
#u870 {
  border-width:0px;
  position:absolute;
  left:1109px;
  top:798px;
  width:763px;
  height:1px;
  display:flex;
}
#u870 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u870_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u871_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u871 {
  border-width:0px;
  position:absolute;
  left:1083px;
  top:993px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u871 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u871_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u872_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u872 {
  border-width:0px;
  position:absolute;
  left:1083px;
  top:871px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u872 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u872_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u873_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u873 {
  border-width:0px;
  position:absolute;
  left:1076px;
  top:789px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u873 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u873_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u874_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u874 {
  border-width:0px;
  position:absolute;
  left:1083px;
  top:911px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u874 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u874_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u875_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u875 {
  border-width:0px;
  position:absolute;
  left:1083px;
  top:830px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u875 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u875_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u876_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:764px;
  height:2px;
}
#u876 {
  border-width:0px;
  position:absolute;
  left:1109px;
  top:961px;
  width:763px;
  height:1px;
  display:flex;
}
#u876 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u876_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u877_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u877 {
  border-width:0px;
  position:absolute;
  left:1083px;
  top:952px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u877 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u877_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u878_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:749px;
  height:2px;
}
#u878 {
  border-width:0px;
  position:absolute;
  left:1109px;
  top:1002px;
  width:748px;
  height:1px;
  display:flex;
}
#u878 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u878_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u879_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u879 {
  border-width:0px;
  position:absolute;
  left:1133px;
  top:1003px;
  width:1px;
  height:5px;
  display:flex;
}
#u879 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u879_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u880_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u880 {
  border-width:0px;
  position:absolute;
  left:1110px;
  top:1013px;
  width:48px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u880 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u880_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u881_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u881 {
  border-width:0px;
  position:absolute;
  left:1208px;
  top:1003px;
  width:1px;
  height:5px;
  display:flex;
}
#u881 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u881_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u882_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u882 {
  border-width:0px;
  position:absolute;
  left:1185px;
  top:1013px;
  width:48px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u882 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u882_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u883_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u883 {
  border-width:0px;
  position:absolute;
  left:1284px;
  top:1003px;
  width:1px;
  height:5px;
  display:flex;
}
#u883 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u883_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u884_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u884 {
  border-width:0px;
  position:absolute;
  left:1261px;
  top:1013px;
  width:48px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u884 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u884_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u885_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u885 {
  border-width:0px;
  position:absolute;
  left:1359px;
  top:1003px;
  width:1px;
  height:5px;
  display:flex;
}
#u885 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u885_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u886_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u886 {
  border-width:0px;
  position:absolute;
  left:1336px;
  top:1013px;
  width:48px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u886 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u886_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u887_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u887 {
  border-width:0px;
  position:absolute;
  left:1434px;
  top:1003px;
  width:1px;
  height:5px;
  display:flex;
}
#u887 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u887_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u888_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u888 {
  border-width:0px;
  position:absolute;
  left:1411px;
  top:1013px;
  width:48px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u888 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u888_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u889_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u889 {
  border-width:0px;
  position:absolute;
  left:1510px;
  top:1003px;
  width:1px;
  height:5px;
  display:flex;
}
#u889 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u889_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u890_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u890 {
  border-width:0px;
  position:absolute;
  left:1487px;
  top:1013px;
  width:48px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u890 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u890_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u891_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u891 {
  border-width:0px;
  position:absolute;
  left:1585px;
  top:1003px;
  width:1px;
  height:5px;
  display:flex;
}
#u891 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u891_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u892_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u892 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:1013px;
  width:48px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u892 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u892_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u893_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u893 {
  border-width:0px;
  position:absolute;
  left:1660px;
  top:1003px;
  width:1px;
  height:5px;
  display:flex;
}
#u893 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u893_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u894_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u894 {
  border-width:0px;
  position:absolute;
  left:1637px;
  top:1013px;
  width:48px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u894 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u894_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u895_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u895 {
  border-width:0px;
  position:absolute;
  left:1736px;
  top:1003px;
  width:1px;
  height:5px;
  display:flex;
}
#u895 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u895_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u896_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u896 {
  border-width:0px;
  position:absolute;
  left:1713px;
  top:1013px;
  width:48px;
  height:36px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u896 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u896_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u897_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u897 {
  border-width:0px;
  position:absolute;
  left:1811px;
  top:1003px;
  width:1px;
  height:5px;
  display:flex;
}
#u897 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u897_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u898_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u898 {
  border-width:0px;
  position:absolute;
  left:1788px;
  top:1013px;
  width:48px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u898 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u898_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u899_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u899 {
  border-width:0px;
  position:absolute;
  left:1099px;
  top:892px;
  width:70px;
  height:30px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u899 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u899_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u900_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:30px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u900 {
  border-width:0px;
  position:absolute;
  left:1172px;
  top:904px;
  width:74px;
  height:30px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u900 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u900_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u901_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:30px;
  background:inherit;
  background-color:rgba(245, 154, 35, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u901 {
  border-width:0px;
  position:absolute;
  left:1255px;
  top:915px;
  width:60px;
  height:30px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u901 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u901_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u902_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:30px;
  background:inherit;
  background-color:rgba(236, 128, 141, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u902 {
  border-width:0px;
  position:absolute;
  left:1316px;
  top:881px;
  width:87px;
  height:30px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u902 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u902_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u903_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
  background:inherit;
  background-color:rgba(194, 128, 255, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u903 {
  border-width:0px;
  position:absolute;
  left:1386px;
  top:881px;
  width:98px;
  height:30px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u903 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u903_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u904_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:30px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u904 {
  border-width:0px;
  position:absolute;
  left:1465px;
  top:924px;
  width:92px;
  height:30px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u904 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u904_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u905_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(19, 190, 180, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u905 {
  border-width:0px;
  position:absolute;
  left:1531px;
  top:870px;
  width:110px;
  height:30px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u905 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u905_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u906_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:88px;
  height:30px;
  background:inherit;
  background-color:rgba(129, 211, 248, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u906 {
  border-width:0px;
  position:absolute;
  left:1617px;
  top:907px;
  width:88px;
  height:30px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u906 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u906_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u907_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:30px;
  background:inherit;
  background-color:rgba(128, 255, 255, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u907 {
  border-width:0px;
  position:absolute;
  left:1711px;
  top:949px;
  width:52px;
  height:30px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u907 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u907_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u908_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:46px;
  height:30px;
  background:inherit;
  background-color:rgba(202, 249, 130, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u908 {
  border-width:0px;
  position:absolute;
  left:1789px;
  top:937px;
  width:46px;
  height:30px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u908 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u908_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u909_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
  background:inherit;
  background-color:rgba(252, 96, 50, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u909 {
  border-width:0px;
  position:absolute;
  left:1131px;
  top:907px;
  width:6px;
  height:6px;
  display:flex;
}
#u909 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u909_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u910_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
  background:inherit;
  background-color:rgba(252, 96, 50, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u910 {
  border-width:0px;
  position:absolute;
  left:1206px;
  top:919px;
  width:6px;
  height:6px;
  display:flex;
}
#u910 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u910_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u911_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
  background:inherit;
  background-color:rgba(252, 96, 50, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u911 {
  border-width:0px;
  position:absolute;
  left:1282px;
  top:914px;
  width:6px;
  height:6px;
  display:flex;
}
#u911 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u911_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u912_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
  background:inherit;
  background-color:rgba(252, 96, 50, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u912 {
  border-width:0px;
  position:absolute;
  left:1357px;
  top:914px;
  width:6px;
  height:6px;
  display:flex;
}
#u912 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u912_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u913_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
  background:inherit;
  background-color:rgba(252, 96, 50, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u913 {
  border-width:0px;
  position:absolute;
  left:1432px;
  top:889px;
  width:6px;
  height:6px;
  display:flex;
}
#u913 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u913_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u914_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
  background:inherit;
  background-color:rgba(252, 96, 50, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u914 {
  border-width:0px;
  position:absolute;
  left:1508px;
  top:934px;
  width:6px;
  height:6px;
  display:flex;
}
#u914 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u914_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u915_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
  background:inherit;
  background-color:rgba(252, 96, 50, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u915 {
  border-width:0px;
  position:absolute;
  left:1583px;
  top:900px;
  width:6px;
  height:6px;
  display:flex;
}
#u915 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u915_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u916_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
  background:inherit;
  background-color:rgba(252, 96, 50, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u916 {
  border-width:0px;
  position:absolute;
  left:1658px;
  top:926px;
  width:6px;
  height:6px;
  display:flex;
}
#u916 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u916_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u917_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
  background:inherit;
  background-color:rgba(252, 96, 50, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u917 {
  border-width:0px;
  position:absolute;
  left:1734px;
  top:929px;
  width:6px;
  height:6px;
  display:flex;
}
#u917 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u917_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u918_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
  background:inherit;
  background-color:rgba(252, 96, 50, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u918 {
  border-width:0px;
  position:absolute;
  left:1809px;
  top:965px;
  width:6px;
  height:6px;
  display:flex;
}
#u918 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u918_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u919 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u920 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u921 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u922_img {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-10px;
  width:207px;
  height:106px;
}
#u922 {
  border-width:0px;
  position:absolute;
  left:1154px;
  top:864px;
  width:187px;
  height:86px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u922 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u922_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u923_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:167px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u923 {
  border-width:0px;
  position:absolute;
  left:1164px;
  top:891px;
  width:167px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u923 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u923_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u924_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u924 {
  border-width:0px;
  position:absolute;
  left:1164px;
  top:874px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u924 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u924_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u925_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u925 {
  border-width:0px;
  position:absolute;
  left:1169px;
  top:899px;
  width:6px;
  height:6px;
  display:flex;
}
#u925 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u925_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u926_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  text-align:right;
  line-height:12px;
}
#u926 {
  border-width:0px;
  position:absolute;
  left:1256px;
  top:896px;
  width:70px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  text-align:right;
  line-height:12px;
}
#u926 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u926_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u927_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:167px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u927 {
  border-width:0px;
  position:absolute;
  left:1164px;
  top:918px;
  width:167px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u927 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u927_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u928_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u928 {
  border-width:0px;
  position:absolute;
  left:1169px;
  top:926px;
  width:6px;
  height:6px;
  display:flex;
}
#u928 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u928_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u929_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FC6032;
  text-align:right;
  line-height:12px;
}
#u929 {
  border-width:0px;
  position:absolute;
  left:1293px;
  top:923px;
  width:33px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FC6032;
  text-align:right;
  line-height:12px;
}
#u929 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u929_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u930_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u930 {
  border-width:0px;
  position:absolute;
  left:1180px;
  top:896px;
  width:57px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u930 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u930_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u931_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FC6032;
  line-height:12px;
}
#u931 {
  border-width:0px;
  position:absolute;
  left:1180px;
  top:923px;
  width:96px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FC6032;
  line-height:12px;
}
#u931 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u931_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u932_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u932 {
  border-width:0px;
  position:absolute;
  left:1076px;
  top:760px;
  width:84px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u932 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u932_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u933_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:826px;
  height:319px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u933 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:1074px;
  width:826px;
  height:319px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u933 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u933_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u934 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u935_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:773px;
  height:2px;
}
#u935 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:1294px;
  width:772px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(0.12614529243210748deg);
  -moz-transform:rotate(0.12614529243210748deg);
  -ms-transform:rotate(0.12614529243210748deg);
  transform:rotate(0.12614529243210748deg);
}
#u935 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u935_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u936_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:773px;
  height:2px;
}
#u936 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:1227px;
  width:772px;
  height:1px;
  display:flex;
}
#u936 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u936_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u937_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:773px;
  height:2px;
}
#u937 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:1194px;
  width:772px;
  height:1px;
  display:flex;
}
#u937 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u937_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u938_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:773px;
  height:2px;
}
#u938 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:1161px;
  width:772px;
  height:1px;
  display:flex;
}
#u938 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u938_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u939_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:773px;
  height:2px;
}
#u939 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:1127px;
  width:772px;
  height:1px;
  display:flex;
}
#u939 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u939_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u940_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u940 {
  border-width:0px;
  position:absolute;
  left:247px;
  top:1284px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u940 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u940_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u941_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u941 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:1185px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u941 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u941_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u942_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u942 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:1118px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u942 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u942_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u943_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u943 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:1294px;
  width:1px;
  height:5px;
  display:flex;
}
#u943 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u943_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u944_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u944 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:1304px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u944 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u944_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u945_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u945 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:1218px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u945 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u945_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u946_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u946 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:1152px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u946 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u946_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u947_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:773px;
  height:2px;
}
#u947 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:1260px;
  width:772px;
  height:1px;
  display:flex;
}
#u947 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u947_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u948_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u948 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:1251px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u948 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u948_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u949 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u950_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:772px;
  height:44px;
}
#u950 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:1227px;
  width:771px;
  height:43px;
  display:flex;
}
#u950 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u950_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u951_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:772px;
  height:75px;
}
#u951 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:1118px;
  width:771px;
  height:74px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u951 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u951_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u952_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:772px;
  height:75px;
}
#u952 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:1161px;
  width:771px;
  height:74px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u952 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u952_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u953_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:773px;
  height:81px;
}
#u953 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:1135px;
  width:772px;
  height:80px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u953 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u953_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u954_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:772px;
  height:75px;
}
#u954 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:1166px;
  width:771px;
  height:74px;
  display:flex;
}
#u954 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u954_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u955_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:775px;
  height:80px;
}
#u955p000 {
  border-width:0px;
  position:absolute;
  left:-6px;
  top:58px;
  width:132px;
  height:16px;
  -webkit-transform:rotate(-10deg);
  -moz-transform:rotate(-10deg);
  -ms-transform:rotate(-10deg);
  transform:rotate(-10deg);
}
#u955p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:16px;
}
#u955p001 {
  border-width:0px;
  position:absolute;
  left:118px;
  top:50px;
  width:68px;
  height:6px;
  -webkit-transform:rotate(-3deg);
  -moz-transform:rotate(-3deg);
  -ms-transform:rotate(-3deg);
  transform:rotate(-3deg);
}
#u955p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:6px;
}
#u955p002 {
  border-width:0px;
  position:absolute;
  left:178px;
  top:26px;
  width:210px;
  height:22px;
  -webkit-transform:rotate(-8deg);
  -moz-transform:rotate(-8deg);
  -ms-transform:rotate(-8deg);
  transform:rotate(-8deg);
}
#u955p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:22px;
}
#u955p003 {
  border-width:0px;
  position:absolute;
  left:380px;
  top:28px;
  width:80px;
  height:6px;
  -webkit-transform:rotate(8deg);
  -moz-transform:rotate(8deg);
  -ms-transform:rotate(8deg);
  transform:rotate(8deg);
}
#u955p003_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:6px;
}
#u955p004 {
  border-width:0px;
  position:absolute;
  left:454px;
  top:26px;
  width:88px;
  height:8px;
  -webkit-transform:rotate(-9deg);
  -moz-transform:rotate(-9deg);
  -ms-transform:rotate(-9deg);
  transform:rotate(-9deg);
}
#u955p004_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:88px;
  height:8px;
}
#u955p005 {
  border-width:0px;
  position:absolute;
  left:536px;
  top:16px;
  width:82px;
  height:6px;
  -webkit-transform:rotate(-7deg);
  -moz-transform:rotate(-7deg);
  -ms-transform:rotate(-7deg);
  transform:rotate(-7deg);
}
#u955p005_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:6px;
}
#u955p006 {
  border-width:0px;
  position:absolute;
  left:612px;
  top:6px;
  width:94px;
  height:6px;
  -webkit-transform:rotate(-6deg);
  -moz-transform:rotate(-6deg);
  -ms-transform:rotate(-6deg);
  transform:rotate(-6deg);
}
#u955p006_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:6px;
}
#u955p007 {
  border-width:0px;
  position:absolute;
  left:702px;
  top:0px;
  width:72px;
  height:4px;
  -webkit-transform:rotate(-3deg);
  -moz-transform:rotate(-3deg);
  -ms-transform:rotate(-3deg);
  transform:rotate(-3deg);
}
#u955p007_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:4px;
}
#u955.compound {
  width:0px;
  height:0px;
}
#u955 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:1158px;
  width:772px;
  height:77px;
  display:flex;
}
#u955 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u955_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u956_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:775px;
  height:83px;
}
#u956 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:1155px;
  width:772px;
  height:80px;
  display:flex;
}
#u956 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u956_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u957_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:774px;
  height:123px;
}
#u957 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:1135px;
  width:771px;
  height:120px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u957 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u957_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u958_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:168px;
}
#u958 {
  border-width:0px;
  position:absolute;
  left:650px;
  top:1127px;
  width:1px;
  height:167px;
  display:flex;
}
#u958 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u958_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u959 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u960_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u960 {
  border-width:0px;
  position:absolute;
  left:639px;
  top:1181px;
  width:9px;
  height:9px;
  display:flex;
  color:transparent;
}
#u960 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u960_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u961 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u962 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u963 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u964_img {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-10px;
  width:168px;
  height:79px;
}
#u964 {
  border-width:0px;
  position:absolute;
  left:660px;
  top:1170px;
  width:148px;
  height:59px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u964 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u964_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u965_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u965 {
  border-width:0px;
  position:absolute;
  left:670px;
  top:1197px;
  width:128px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u965 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u965_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u966_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u966 {
  border-width:0px;
  position:absolute;
  left:670px;
  top:1180px;
  width:27px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u966 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u966_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u967_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u967 {
  border-width:0px;
  position:absolute;
  left:675px;
  top:1205px;
  width:6px;
  height:6px;
  display:flex;
}
#u967 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u967_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u968_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  text-align:right;
  line-height:12px;
}
#u968 {
  border-width:0px;
  position:absolute;
  left:739px;
  top:1202px;
  width:54px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  text-align:right;
  line-height:12px;
}
#u968 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u968_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u969_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u969 {
  border-width:0px;
  position:absolute;
  left:686px;
  top:1202px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u969 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u969_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u970_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u970 {
  border-width:0px;
  position:absolute;
  left:463px;
  top:1294px;
  width:1px;
  height:5px;
  display:flex;
}
#u970 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u970_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u971_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u971 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:1304px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u971 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u971_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u972_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u972 {
  border-width:0px;
  position:absolute;
  left:650px;
  top:1294px;
  width:1px;
  height:5px;
  display:flex;
}
#u972 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u972_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u973_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u973 {
  border-width:0px;
  position:absolute;
  left:637px;
  top:1304px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u973 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u973_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u974_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u974 {
  border-width:0px;
  position:absolute;
  left:836px;
  top:1294px;
  width:1px;
  height:5px;
  display:flex;
}
#u974 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u974_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u975_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u975 {
  border-width:0px;
  position:absolute;
  left:823px;
  top:1304px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u975 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u975_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u976_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u976 {
  border-width:0px;
  position:absolute;
  left:1022px;
  top:1294px;
  width:1px;
  height:5px;
  display:flex;
}
#u976 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u976_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u977_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u977 {
  border-width:0px;
  position:absolute;
  left:1009px;
  top:1304px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u977 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u977_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u978_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u978 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:1089px;
  width:109px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u978 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u978_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u979_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u979 {
  border-width:0px;
  position:absolute;
  left:357px;
  top:1345px;
  width:10px;
  height:10px;
  display:flex;
}
#u979 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u979_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u980_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u980 {
  border-width:0px;
  position:absolute;
  left:372px;
  top:1344px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u980 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u980_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u981_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u981 {
  border-width:0px;
  position:absolute;
  left:773px;
  top:1345px;
  width:10px;
  height:10px;
  display:flex;
}
#u981 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u981_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u982_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u982 {
  border-width:0px;
  position:absolute;
  left:788px;
  top:1344px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u982 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u982_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u983_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u983 {
  border-width:0px;
  position:absolute;
  left:440px;
  top:1345px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u983 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u983_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u984_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u984 {
  border-width:0px;
  position:absolute;
  left:455px;
  top:1344px;
  width:84px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u984 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u984_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u985_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u985 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:1345px;
  width:10px;
  height:10px;
  display:flex;
}
#u985 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u985_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u986_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u986 {
  border-width:0px;
  position:absolute;
  left:574px;
  top:1344px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u986 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u986_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u987_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u987 {
  border-width:0px;
  position:absolute;
  left:666px;
  top:1345px;
  width:10px;
  height:10px;
  display:flex;
}
#u987 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u987_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u988_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u988 {
  border-width:0px;
  position:absolute;
  left:681px;
  top:1344px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u988 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u988_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u989_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u989 {
  border-width:0px;
  position:absolute;
  left:856px;
  top:1346px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u989 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u989_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u990_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u990 {
  border-width:0px;
  position:absolute;
  left:871px;
  top:1345px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u990 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u990_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u991_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u991 {
  border-width:0px;
  position:absolute;
  left:440px;
  top:1367px;
  width:10px;
  height:10px;
  display:flex;
}
#u991 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u991_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u992_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u992 {
  border-width:0px;
  position:absolute;
  left:455px;
  top:1366px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u992 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u992_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u993_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u993 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:1367px;
  width:10px;
  height:10px;
  display:flex;
}
#u993 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u993_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u994_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u994 {
  border-width:0px;
  position:absolute;
  left:574px;
  top:1366px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u994 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u994_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u995_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(19, 190, 180, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u995 {
  border-width:0px;
  position:absolute;
  left:666px;
  top:1367px;
  width:10px;
  height:10px;
  display:flex;
}
#u995 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u995_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u996_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u996 {
  border-width:0px;
  position:absolute;
  left:681px;
  top:1366px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u996 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u996_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u997_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u997 {
  border-width:0px;
  position:absolute;
  left:773px;
  top:1367px;
  width:10px;
  height:10px;
  display:flex;
}
#u997 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u997_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u998_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u998 {
  border-width:0px;
  position:absolute;
  left:788px;
  top:1366px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u998 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u998_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u999_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:826px;
  height:319px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u999 {
  border-width:0px;
  position:absolute;
  left:1061px;
  top:1074px;
  width:826px;
  height:319px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u999 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u999_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1000 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:773px;
  height:2px;
}
#u1001 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:1294px;
  width:772px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(0.12614529243210748deg);
  -moz-transform:rotate(0.12614529243210748deg);
  -ms-transform:rotate(0.12614529243210748deg);
  transform:rotate(0.12614529243210748deg);
}
#u1001 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1001_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:773px;
  height:2px;
}
#u1002 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:1227px;
  width:772px;
  height:1px;
  display:flex;
}
#u1002 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1002_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1003_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:773px;
  height:2px;
}
#u1003 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:1194px;
  width:772px;
  height:1px;
  display:flex;
}
#u1003 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1003_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1004_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:773px;
  height:2px;
}
#u1004 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:1161px;
  width:772px;
  height:1px;
  display:flex;
}
#u1004 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1004_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1005_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:773px;
  height:2px;
}
#u1005 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:1127px;
  width:772px;
  height:1px;
  display:flex;
}
#u1005 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1005_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1006_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u1006 {
  border-width:0px;
  position:absolute;
  left:1083px;
  top:1284px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u1006 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1006_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1007_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u1007 {
  border-width:0px;
  position:absolute;
  left:1076px;
  top:1185px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u1007 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1007_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1008_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u1008 {
  border-width:0px;
  position:absolute;
  left:1076px;
  top:1118px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u1008 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1008_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1009_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1009 {
  border-width:0px;
  position:absolute;
  left:1113px;
  top:1294px;
  width:1px;
  height:5px;
  display:flex;
}
#u1009 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1009_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1010_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1010 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:1304px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1010 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1010_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1011_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u1011 {
  border-width:0px;
  position:absolute;
  left:1076px;
  top:1218px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u1011 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1011_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1012_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u1012 {
  border-width:0px;
  position:absolute;
  left:1076px;
  top:1152px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u1012 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1012_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1013_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:773px;
  height:2px;
}
#u1013 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:1260px;
  width:772px;
  height:1px;
  display:flex;
}
#u1013 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1013_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1014_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u1014 {
  border-width:0px;
  position:absolute;
  left:1076px;
  top:1251px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(127, 127, 127, 0.6470588235294118);
  text-align:right;
  line-height:20px;
}
#u1014 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1014_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1015 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1016_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:772px;
  height:44px;
}
#u1016 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:1227px;
  width:771px;
  height:43px;
  display:flex;
}
#u1016 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1016_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1017_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:772px;
  height:75px;
}
#u1017 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:1118px;
  width:771px;
  height:74px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u1017 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1017_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1018_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:772px;
  height:75px;
}
#u1018 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:1161px;
  width:771px;
  height:74px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u1018 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1018_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1019_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:773px;
  height:81px;
}
#u1019 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:1135px;
  width:772px;
  height:80px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u1019 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1019_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1020_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:772px;
  height:75px;
}
#u1020 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:1166px;
  width:771px;
  height:74px;
  display:flex;
}
#u1020 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1020_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1021_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:775px;
  height:80px;
}
#u1021p000 {
  border-width:0px;
  position:absolute;
  left:-6px;
  top:58px;
  width:132px;
  height:16px;
  -webkit-transform:rotate(-10deg);
  -moz-transform:rotate(-10deg);
  -ms-transform:rotate(-10deg);
  transform:rotate(-10deg);
}
#u1021p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:16px;
}
#u1021p001 {
  border-width:0px;
  position:absolute;
  left:118px;
  top:50px;
  width:68px;
  height:6px;
  -webkit-transform:rotate(-3deg);
  -moz-transform:rotate(-3deg);
  -ms-transform:rotate(-3deg);
  transform:rotate(-3deg);
}
#u1021p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:6px;
}
#u1021p002 {
  border-width:0px;
  position:absolute;
  left:178px;
  top:26px;
  width:210px;
  height:22px;
  -webkit-transform:rotate(-8deg);
  -moz-transform:rotate(-8deg);
  -ms-transform:rotate(-8deg);
  transform:rotate(-8deg);
}
#u1021p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:22px;
}
#u1021p003 {
  border-width:0px;
  position:absolute;
  left:380px;
  top:28px;
  width:80px;
  height:6px;
  -webkit-transform:rotate(8deg);
  -moz-transform:rotate(8deg);
  -ms-transform:rotate(8deg);
  transform:rotate(8deg);
}
#u1021p003_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:6px;
}
#u1021p004 {
  border-width:0px;
  position:absolute;
  left:454px;
  top:26px;
  width:88px;
  height:8px;
  -webkit-transform:rotate(-9deg);
  -moz-transform:rotate(-9deg);
  -ms-transform:rotate(-9deg);
  transform:rotate(-9deg);
}
#u1021p004_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:88px;
  height:8px;
}
#u1021p005 {
  border-width:0px;
  position:absolute;
  left:536px;
  top:16px;
  width:82px;
  height:6px;
  -webkit-transform:rotate(-7deg);
  -moz-transform:rotate(-7deg);
  -ms-transform:rotate(-7deg);
  transform:rotate(-7deg);
}
#u1021p005_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:6px;
}
#u1021p006 {
  border-width:0px;
  position:absolute;
  left:612px;
  top:6px;
  width:94px;
  height:6px;
  -webkit-transform:rotate(-6deg);
  -moz-transform:rotate(-6deg);
  -ms-transform:rotate(-6deg);
  transform:rotate(-6deg);
}
#u1021p006_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:6px;
}
#u1021p007 {
  border-width:0px;
  position:absolute;
  left:702px;
  top:0px;
  width:72px;
  height:4px;
  -webkit-transform:rotate(-3deg);
  -moz-transform:rotate(-3deg);
  -ms-transform:rotate(-3deg);
  transform:rotate(-3deg);
}
#u1021p007_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:4px;
}
#u1021.compound {
  width:0px;
  height:0px;
}
#u1021 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:1158px;
  width:772px;
  height:77px;
  display:flex;
}
#u1021 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1021_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1022_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:775px;
  height:83px;
}
#u1022 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:1155px;
  width:772px;
  height:80px;
  display:flex;
}
#u1022 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1022_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1023_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:774px;
  height:123px;
}
#u1023 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:1135px;
  width:771px;
  height:120px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u1023 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1023_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1024_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:168px;
}
#u1024 {
  border-width:0px;
  position:absolute;
  left:1486px;
  top:1127px;
  width:1px;
  height:167px;
  display:flex;
}
#u1024 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1024_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1025 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1026_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u1026 {
  border-width:0px;
  position:absolute;
  left:1475px;
  top:1181px;
  width:9px;
  height:9px;
  display:flex;
  color:transparent;
}
#u1026 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1026_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1027 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1028 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1029 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1030_img {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-10px;
  width:168px;
  height:79px;
}
#u1030 {
  border-width:0px;
  position:absolute;
  left:1496px;
  top:1170px;
  width:148px;
  height:59px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1030 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1030_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1031_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1031 {
  border-width:0px;
  position:absolute;
  left:1506px;
  top:1197px;
  width:128px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1031 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1031_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1032_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u1032 {
  border-width:0px;
  position:absolute;
  left:1506px;
  top:1180px;
  width:27px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u1032 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1032_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1033_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u1033 {
  border-width:0px;
  position:absolute;
  left:1511px;
  top:1205px;
  width:6px;
  height:6px;
  display:flex;
}
#u1033 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1033_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1034_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  text-align:right;
  line-height:12px;
}
#u1034 {
  border-width:0px;
  position:absolute;
  left:1575px;
  top:1202px;
  width:54px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  text-align:right;
  line-height:12px;
}
#u1034 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1034_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1035_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u1035 {
  border-width:0px;
  position:absolute;
  left:1522px;
  top:1202px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u1035 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1035_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1036_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1036 {
  border-width:0px;
  position:absolute;
  left:1299px;
  top:1294px;
  width:1px;
  height:5px;
  display:flex;
}
#u1036 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1036_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1037_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1037 {
  border-width:0px;
  position:absolute;
  left:1286px;
  top:1304px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1037 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1037_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1038_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1038 {
  border-width:0px;
  position:absolute;
  left:1486px;
  top:1294px;
  width:1px;
  height:5px;
  display:flex;
}
#u1038 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1038_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1039_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1039 {
  border-width:0px;
  position:absolute;
  left:1473px;
  top:1304px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1039 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1039_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1040_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1040 {
  border-width:0px;
  position:absolute;
  left:1672px;
  top:1294px;
  width:1px;
  height:5px;
  display:flex;
}
#u1040 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1040_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1041_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1041 {
  border-width:0px;
  position:absolute;
  left:1659px;
  top:1304px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1041 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1041_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1042_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1042 {
  border-width:0px;
  position:absolute;
  left:1858px;
  top:1294px;
  width:1px;
  height:5px;
  display:flex;
}
#u1042 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1042_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1043_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1043 {
  border-width:0px;
  position:absolute;
  left:1845px;
  top:1304px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:20px;
}
#u1043 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1043_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1044_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1044 {
  border-width:0px;
  position:absolute;
  left:1076px;
  top:1089px;
  width:95px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1044 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1044_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1045_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1045 {
  border-width:0px;
  position:absolute;
  left:1193px;
  top:1345px;
  width:10px;
  height:10px;
  display:flex;
}
#u1045 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1045_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1046_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1046 {
  border-width:0px;
  position:absolute;
  left:1208px;
  top:1344px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1046 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1046_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1047_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1047 {
  border-width:0px;
  position:absolute;
  left:1609px;
  top:1345px;
  width:10px;
  height:10px;
  display:flex;
}
#u1047 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1047_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1048_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u1048 {
  border-width:0px;
  position:absolute;
  left:1624px;
  top:1344px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u1048 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1048_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1049_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u1049 {
  border-width:0px;
  position:absolute;
  left:1276px;
  top:1345px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u1049 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1049_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1050_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1050 {
  border-width:0px;
  position:absolute;
  left:1291px;
  top:1344px;
  width:84px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1050 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1050_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1051_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1051 {
  border-width:0px;
  position:absolute;
  left:1395px;
  top:1345px;
  width:10px;
  height:10px;
  display:flex;
}
#u1051 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1051_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1052_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u1052 {
  border-width:0px;
  position:absolute;
  left:1410px;
  top:1344px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u1052 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1052_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1053_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1053 {
  border-width:0px;
  position:absolute;
  left:1502px;
  top:1345px;
  width:10px;
  height:10px;
  display:flex;
}
#u1053 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1053_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1054_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u1054 {
  border-width:0px;
  position:absolute;
  left:1517px;
  top:1344px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u1054 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1054_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1055_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u1055 {
  border-width:0px;
  position:absolute;
  left:1692px;
  top:1346px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u1055 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1055_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1056_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u1056 {
  border-width:0px;
  position:absolute;
  left:1707px;
  top:1345px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u1056 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1056_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1057_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1057 {
  border-width:0px;
  position:absolute;
  left:1276px;
  top:1367px;
  width:10px;
  height:10px;
  display:flex;
}
#u1057 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1057_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1058_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u1058 {
  border-width:0px;
  position:absolute;
  left:1291px;
  top:1366px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u1058 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1058_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1059_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1059 {
  border-width:0px;
  position:absolute;
  left:1395px;
  top:1367px;
  width:10px;
  height:10px;
  display:flex;
}
#u1059 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1059_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1060_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u1060 {
  border-width:0px;
  position:absolute;
  left:1410px;
  top:1366px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u1060 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1060_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1061_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(19, 190, 180, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1061 {
  border-width:0px;
  position:absolute;
  left:1502px;
  top:1367px;
  width:10px;
  height:10px;
  display:flex;
}
#u1061 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1061_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1062_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1062 {
  border-width:0px;
  position:absolute;
  left:1517px;
  top:1366px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1062 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1062_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1063_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1063 {
  border-width:0px;
  position:absolute;
  left:1609px;
  top:1367px;
  width:10px;
  height:10px;
  display:flex;
}
#u1063 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1063_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1064_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u1064 {
  border-width:0px;
  position:absolute;
  left:1624px;
  top:1366px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#AAAAAA;
}
#u1064 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1064_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1065_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:313px;
  height:94px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:20px;
}
#u1065 {
  border-width:0px;
  position:absolute;
  left:1962px;
  top:102px;
  width:313px;
  height:94px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:20px;
}
#u1065 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1065_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1066 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1067_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:40px;
}
#u1067 {
  border-width:0px;
  position:absolute;
  left:1704px;
  top:160px;
  width:198px;
  height:40px;
  display:flex;
}
#u1067 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1067_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1068_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u1068 {
  border-width:0px;
  position:absolute;
  left:1719px;
  top:173px;
  width:112px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u1068 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1068_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1069_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1069 {
  border-width:0px;
  position:absolute;
  left:1841px;
  top:165px;
  width:30px;
  height:30px;
  display:flex;
}
#u1069 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1069_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1070_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:20px;
}
#u1070 {
  border-width:0px;
  position:absolute;
  left:1847px;
  top:170px;
  width:18px;
  height:20px;
  display:flex;
}
#u1070 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1070_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1071_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:10px;
}
#u1071 {
  border-width:0px;
  position:absolute;
  left:1881px;
  top:175px;
  width:6px;
  height:10px;
  display:flex;
}
#u1071 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1071_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1072 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1073_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:40px;
}
#u1073 {
  border-width:0px;
  position:absolute;
  left:1704px;
  top:695px;
  width:198px;
  height:40px;
  display:flex;
}
#u1073 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1073_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1074_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u1074 {
  border-width:0px;
  position:absolute;
  left:1719px;
  top:708px;
  width:112px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u1074 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1074_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1075_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1075 {
  border-width:0px;
  position:absolute;
  left:1841px;
  top:700px;
  width:30px;
  height:30px;
  display:flex;
}
#u1075 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1075_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1076_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:20px;
}
#u1076 {
  border-width:0px;
  position:absolute;
  left:1847px;
  top:705px;
  width:18px;
  height:20px;
  display:flex;
}
#u1076 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1076_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1077_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:10px;
}
#u1077 {
  border-width:0px;
  position:absolute;
  left:1881px;
  top:710px;
  width:6px;
  height:10px;
  display:flex;
}
#u1077 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1077_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
