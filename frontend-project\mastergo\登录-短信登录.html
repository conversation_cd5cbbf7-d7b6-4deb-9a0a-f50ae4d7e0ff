﻿<!DOCTYPE html>
<html>
  <head>
    <title>登录-短信登录</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/登录-短信登录/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/登录-短信登录/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u27" class="ax_default box_2">
        <div id="u27_div" class=""></div>
        <div id="u27_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u28" class="ax_default _图片">
        <img id="u28_img" class="img " src="images/登录-密码登录/u1.png"/>
        <div id="u28_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u29" class="ax_default box_2">
        <div id="u29_div" class=""></div>
        <div id="u29_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u30" class="ax_default" data-left="40" data-top="40" data-width="195" data-height="40" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u31" class="ax_default _三级标题">
          <div id="u31_div" class=""></div>
          <div id="u31_text" class="text ">
            <p><span>rev-REITs平台</span></p>
          </div>
        </div>

        <!-- Unnamed (圆形) -->
        <div id="u32" class="ax_default ellipse">
          <img id="u32_img" class="img " src="images/登录-密码登录/u5.svg"/>
          <div id="u32_text" class="text ">
            <p><span>logo</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u33" class="ax_default box_2">
        <div id="u33_div" class=""></div>
        <div id="u33_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u34" class="ax_default _三级标题">
        <div id="u34_div" class=""></div>
        <div id="u34_text" class="text ">
          <p><span>密码登录</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u35" class="ax_default" data-left="1547" data-top="243" data-width="155" data-height="16" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u36" class="ax_default _三级标题">
          <div id="u36_div" class=""></div>
          <div id="u36_text" class="text ">
            <p><span>还没有账号，去</span><span style="color:#1868F1;">注册</span></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u37" class="ax_default icon">
          <img id="u37_img" class="img " src="images/登录-密码登录/u10.svg"/>
          <div id="u37_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u38" class="ax_default box_2">
        <div id="u38_div" class=""></div>
        <div id="u38_text" class="text ">
          <p><span>登录</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u39" class="ax_default" data-left="1202" data-top="507" data-width="268" data-height="12" layer-opacity="1">

        <!-- Unnamed (复选框) -->
        <div id="u40" class="ax_default checkbox">
          <label id="u40_input_label" for="u40_input" style="position: absolute; left: 0px;">
            <img id="u40_img" class="img " src="images/登录-密码登录/u17.svg"/>
            <div id="u40_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </label>
          <input id="u40_input" type="checkbox" value="checkbox"/>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u41" class="ax_default _三级标题">
          <div id="u41_div" class=""></div>
          <div id="u41_text" class="text ">
            <p><span>阅读并同意</span><span style="color:#1868F1;">用户协议</span><span>、</span><span style="color:#1868F1;">隐私声明</span><span>、</span><span style="color:#1868F1;">产品使用条款</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u42" class="ax_default _三级标题">
        <div id="u42_div" class=""></div>
        <div id="u42_text" class="text ">
          <p><span>短信登录</span></p>
        </div>
      </div>

      <!-- Unnamed (直线) -->
      <div id="u43" class="ax_default line">
        <img id="u43_img" class="img " src="images/登录-密码登录/u21.svg"/>
        <div id="u43_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u44" class="ax_default _三级标题">
        <div id="u44_div" class=""></div>
        <div id="u44_text" class="text ">
          <p><span>忘记密码</span></p>
        </div>
      </div>

      <!-- Unnamed (直线) -->
      <div id="u45" class="ax_default line">
        <img id="u45_img" class="img " src="images/登录-密码登录/u23.svg"/>
        <div id="u45_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u46" class="ax_default _三级标题">
        <div id="u46_div" class=""></div>
        <div id="u46_text" class="text ">
          <p><span>其他登录方式</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u47" class="ax_default _三级标题">
        <div id="u47_div" class=""></div>
        <div id="u47_text" class="text ">
          <p><span>中国REITs论坛账号登录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u48" class="ax_default _三级标题">
        <div id="u48_div" class=""></div>
        <div id="u48_text" class="text ">
          <p><span>手&nbsp; 机&nbsp; 号</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u49" class="ax_default _三级标题">
        <div id="u49_div" class=""></div>
        <div id="u49_text" class="text ">
          <p><span>验&nbsp; 证&nbsp; 码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u50" class="ax_default box_2">
        <div id="u50_div" class=""></div>
        <div id="u50_text" class="text ">
          <p><span>请输入用户名/手机号</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u51" class="ax_default box_2">
        <div id="u51_div" class=""></div>
        <div id="u51_text" class="text ">
          <p><span>请输入验证码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u52" class="ax_default box_2">
        <div id="u52_div" class=""></div>
        <div id="u52_text" class="text ">
          <p><span>获取验证码</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
