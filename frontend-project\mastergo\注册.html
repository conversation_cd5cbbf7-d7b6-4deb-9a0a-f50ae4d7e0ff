﻿<!DOCTYPE html>
<html>
  <head>
    <title>注册</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/注册/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/注册/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u53" class="ax_default box_2">
        <div id="u53_div" class=""></div>
        <div id="u53_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u54" class="ax_default _图片">
        <img id="u54_img" class="img " src="images/登录-密码登录/u1.png"/>
        <div id="u54_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u55" class="ax_default box_2">
        <div id="u55_div" class=""></div>
        <div id="u55_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u56" class="ax_default" data-left="40" data-top="40" data-width="195" data-height="40" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u57" class="ax_default _三级标题">
          <div id="u57_div" class=""></div>
          <div id="u57_text" class="text ">
            <p><span>rev-REITs平台</span></p>
          </div>
        </div>

        <!-- Unnamed (圆形) -->
        <div id="u58" class="ax_default ellipse">
          <img id="u58_img" class="img " src="images/登录-密码登录/u5.svg"/>
          <div id="u58_text" class="text ">
            <p><span>logo</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u59" class="ax_default box_2">
        <div id="u59_div" class=""></div>
        <div id="u59_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u60" class="ax_default _三级标题">
        <div id="u60_div" class=""></div>
        <div id="u60_text" class="text ">
          <p><span>欢迎注册</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u61" class="ax_default" data-left="1563" data-top="228" data-width="139" data-height="16" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u62" class="ax_default _三级标题">
          <div id="u62_div" class=""></div>
          <div id="u62_text" class="text ">
            <p><span>已有账号，去</span><span style="color:#1868F1;">登录</span></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u63" class="ax_default icon">
          <img id="u63_img" class="img " src="images/登录-密码登录/u10.svg"/>
          <div id="u63_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u64" class="ax_default _三级标题">
        <div id="u64_div" class=""></div>
        <div id="u64_text" class="text ">
          <p><span>手&nbsp; 机&nbsp; 号</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u65" class="ax_default box_2">
        <div id="u65_div" class=""></div>
        <div id="u65_text" class="text ">
          <p><span>可用于登录和找回密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u66" class="ax_default _三级标题">
        <div id="u66_div" class=""></div>
        <div id="u66_text" class="text ">
          <p><span>用&nbsp; 户&nbsp; 名</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u67" class="ax_default box_2">
        <div id="u67_div" class=""></div>
        <div id="u67_text" class="text ">
          <p><span>请设置登录用户名</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u68" class="ax_default _三级标题">
        <div id="u68_div" class=""></div>
        <div id="u68_text" class="text ">
          <p><span>密&nbsp; &nbsp; &nbsp; &nbsp; 码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u69" class="ax_default box_2">
        <div id="u69_div" class=""></div>
        <div id="u69_text" class="text ">
          <p><span>请设置登录密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u70" class="ax_default _三级标题">
        <div id="u70_div" class=""></div>
        <div id="u70_text" class="text ">
          <p><span>确认密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u71" class="ax_default box_2">
        <div id="u71_div" class=""></div>
        <div id="u71_text" class="text ">
          <p><span>请再次确认登录密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u72" class="ax_default _三级标题">
        <div id="u72_div" class=""></div>
        <div id="u72_text" class="text ">
          <p><span>验&nbsp; 证&nbsp; 码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u73" class="ax_default box_2">
        <div id="u73_div" class=""></div>
        <div id="u73_text" class="text ">
          <p><span>请输入验证码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u74" class="ax_default box_2">
        <div id="u74_div" class=""></div>
        <div id="u74_text" class="text ">
          <p><span>获取验证码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u75" class="ax_default box_2">
        <div id="u75_div" class=""></div>
        <div id="u75_text" class="text ">
          <p><span>注册</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u76" class="ax_default" data-left="1202" data-top="660" data-width="268" data-height="12" layer-opacity="1">

        <!-- Unnamed (复选框) -->
        <div id="u77" class="ax_default checkbox">
          <label id="u77_input_label" for="u77_input" style="position: absolute; left: 0px;">
            <img id="u77_img" class="img " src="images/注册/u77.svg"/>
            <div id="u77_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </label>
          <input id="u77_input" type="checkbox" value="checkbox"/>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u78" class="ax_default _三级标题">
          <div id="u78_div" class=""></div>
          <div id="u78_text" class="text ">
            <p><span>阅读并同意</span><span style="color:#1868F1;">用户协议</span><span>、</span><span style="color:#1868F1;">隐私声明</span><span>、</span><span style="color:#1868F1;">产品使用条款</span></p>
          </div>
        </div>
      </div>

      <!-- 预约 (组合) -->
      <div id="u79" class="ax_default" data-label="预约" data-left="1286" data-top="405" data-width="192" data-height="36" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u80" class="ax_default _形状1">
          <div id="u80_div" class=""></div>
          <div id="u80_text" class="text ">
            <p><span>5-12个字符，需包含字母或汉字</span></p>
          </div>
        </div>

        <!-- Unnamed (Triangle Down) -->
        <div id="u81" class="ax_default _形状1">
          <img id="u81_img" class="img " src="images/注册/u81.svg"/>
          <div id="u81_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- 预约 (组合) -->
      <div id="u82" class="ax_default" data-label="预约" data-left="1286" data-top="475" data-width="275" data-height="36" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u83" class="ax_default _形状1">
          <div id="u83_div" class=""></div>
          <div id="u83_text" class="text ">
            <p><span>6-20个字符，需包含字母、数字，不能包含空格</span></p>
          </div>
        </div>

        <!-- Unnamed (Triangle Down) -->
        <div id="u84" class="ax_default _形状1">
          <img id="u84_img" class="img " src="images/注册/u84.svg"/>
          <div id="u84_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u85" class="ax_default _图片">
        <img id="u85_img" class="img " src="images/登录-密码登录/u19.png"/>
        <div id="u85_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u86" class="ax_default _图片">
        <img id="u86_img" class="img " src="images/登录-密码登录/u19.png"/>
        <div id="u86_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
