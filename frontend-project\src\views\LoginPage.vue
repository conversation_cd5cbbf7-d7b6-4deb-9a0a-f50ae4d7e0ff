<template>
  <div class="login-bg">
    <!-- 顶部 Logo 区域 -->
    <div class="header-logo">
      <div class="login-logo">
        <span>logo</span>
      </div>
      <span class="brand-title">rev-REITs平台</span>
    </div>

    <!-- 右上角注册链接 -->
    <div class="header-register">
      <div class="register-link">
        <span style="color: #333333;">还没有账号，去</span>
        <a href="#" class="login-link">注册</a>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="login-container">
      <!-- 登录表单卡片 -->
      <div class="login-card login-card-positioned">
        <div style="padding: 40px;">
          <!-- 标签切换 -->
          <div style="display: flex; gap: 40px; margin-bottom: 30px;">
            <button 
              :class="['tab-button', activeTab === 'password' ? 'active' : '']"
              @click="activeTab = 'password'"
            >
              密码登录
            </button>
            <button 
              :class="['tab-button', activeTab === 'sms' ? 'active' : '']"
              @click="activeTab = 'sms'"
            >
              短信登录
            </button>
          </div>

          <!-- 忘记密码链接 -->
          <div style="text-align: right; margin-bottom: 20px;">
            <a href="#" class="login-link">忘记密码</a>
          </div>

          <!-- 登录表单 -->
          <form @submit.prevent="handleLogin">
            <!-- 用户名 -->
            <div style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 8px;">用户名</label>
              <input
                v-model="formData.username"
                type="text"
                placeholder="请输入用户名/手机号"
                class="login-input"
                required
              />
            </div>

            <!-- 密码 -->
            <div style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 8px;">密码</label>
              <input
                v-model="formData.password"
                type="password"
                placeholder="请输入登录密码"
                class="login-input"
                required
                minlength="6"
              />
            </div>

            <!-- 协议复选框 -->
            <div style="margin-bottom: 30px;">
              <label class="agreement-checkbox">
                <input
                  v-model="formData.remember"
                  type="checkbox"
                />
                阅读并同意
                <a href="#" class="login-link">用户协议</a>、
                <a href="#" class="login-link">隐私声明</a>、
                <a href="#" class="login-link">产品使用条款</a>
              </label>
            </div>

            <!-- 登录按钮 -->
            <div style="text-align: center; margin-bottom: 30px;">
              <button
                type="submit"
                :disabled="loading"
                class="login-button"
              >
                <span v-if="loading">登录中...</span>
                <span v-else>登录</span>
              </button>
            </div>
          </form>

          <!-- 其他登录方式 -->
          <div style="text-align: center;">
            <div class="divider">
              <span>其他登录方式</span>
            </div>
            <div style="margin-top: 15px; color: #7F7F7F;">
              中国REITs论坛账号登录
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { useUserStore } from '@/stores'
import { useRouter } from 'vue-router'
import '@/assets/login.css'

const router = useRouter()
const userStore = useUserStore()

const activeTab = ref('password')
const loading = ref(false)

const formData = reactive({
  username: '',
  password: '',
  remember: false
})

const handleLogin = async () => {
  try {
    loading.value = true
    
    // 模拟登录API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新用户状态
    userStore.login({
      name: formData.username,
      email: formData.username.includes('@') ? formData.username : `${formData.username}@example.com`
    })
    
    message.success('登录成功！')
    
    // 跳转到首页
    router.push('/')
    
  } catch (error) {
    message.error('登录失败，请检查用户名和密码')
  } finally {
    loading.value = false
  }
}
</script>
