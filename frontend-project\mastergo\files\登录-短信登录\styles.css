﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:1912px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u27_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u27 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  display:flex;
}
#u27 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u27_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u28_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
}
#u28 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  display:flex;
}
#u28 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u28_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u29_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.4980392156862745);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u29 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  display:flex;
}
#u29 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u29_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u30 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u31_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#1868F1;
}
#u31 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:48px;
  width:145px;
  height:25px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#1868F1;
}
#u31 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u31_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u32_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u32 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:40px;
  width:40px;
  height:40px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u32 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u32_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u33_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:560px;
  height:551px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.8);
  border:none;
  border-radius:20px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u33 {
  border-width:0px;
  position:absolute;
  left:1172px;
  top:201px;
  width:560px;
  height:551px;
  display:flex;
}
#u33 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u33_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u34_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  text-align:center;
}
#u34 {
  border-width:0px;
  position:absolute;
  left:1213px;
  top:241px;
  width:80px;
  height:21px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  text-align:center;
}
#u34 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u34_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u35 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u36_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u36 {
  border-width:0px;
  position:absolute;
  left:1547px;
  top:243px;
  width:144px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u36 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u36_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u37_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:10px;
}
#u37 {
  border-width:0px;
  position:absolute;
  left:1696px;
  top:246px;
  width:6px;
  height:10px;
  display:flex;
}
#u37 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u37_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u38_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:25px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u38 {
  border-width:0px;
  position:absolute;
  left:1202px;
  top:529px;
  width:500px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u38 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u38_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u39 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u40 label {
  left:0px;
  width:100%;
}
#u40_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u40 {
  border-width:0px;
  position:absolute;
  left:1202px;
  top:507px;
  width:16px;
  height:12px;
  display:flex;
}
#u40 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u40_img.selected {
}
#u40.selected {
}
#u40_img.disabled {
}
#u40.disabled {
}
#u40_img.selected.error {
}
#u40.selected.error {
}
#u40_img.selected.hint {
}
#u40.selected.hint {
}
#u40_img.selected.error.hint {
}
#u40.selected.error.hint {
}
#u40_img.mouseOver.selected {
}
#u40.mouseOver.selected {
}
#u40_img.mouseOver.selected.error {
}
#u40.mouseOver.selected.error {
}
#u40_img.mouseOver.selected.hint {
}
#u40.mouseOver.selected.hint {
}
#u40_img.mouseOver.selected.error.hint {
}
#u40.mouseOver.selected.error.hint {
}
#u40_img.mouseDown.selected {
}
#u40.mouseDown.selected {
}
#u40_img.mouseDown.selected.error {
}
#u40.mouseDown.selected.error {
}
#u40_img.mouseDown.selected.hint {
}
#u40.mouseDown.selected.hint {
}
#u40_img.mouseDown.selected.error.hint {
}
#u40.mouseDown.selected.error.hint {
}
#u40_img.mouseOver.mouseDown.selected {
}
#u40.mouseOver.mouseDown.selected {
}
#u40_img.mouseOver.mouseDown.selected.error {
}
#u40.mouseOver.mouseDown.selected.error {
}
#u40_img.mouseOver.mouseDown.selected.hint {
}
#u40.mouseOver.mouseDown.selected.hint {
}
#u40_img.mouseOver.mouseDown.selected.error.hint {
}
#u40.mouseOver.mouseDown.selected.error.hint {
}
#u40_img.focused.selected {
}
#u40.focused.selected {
}
#u40_img.focused.selected.error {
}
#u40.focused.selected.error {
}
#u40_img.focused.selected.hint {
}
#u40.focused.selected.hint {
}
#u40_img.focused.selected.error.hint {
}
#u40.focused.selected.error.hint {
}
#u40_img.selected.disabled {
}
#u40.selected.disabled {
}
#u40_img.selected.hint.disabled {
}
#u40.selected.hint.disabled {
}
#u40_img.selected.error.disabled {
}
#u40.selected.error.disabled {
}
#u40_img.selected.error.hint.disabled {
}
#u40.selected.error.hint.disabled {
}
#u40_text {
  border-width:0px;
  position:absolute;
  left:12px;
  top:0px;
  width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u40_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u41_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u41 {
  border-width:0px;
  position:absolute;
  left:1218px;
  top:507px;
  width:252px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u41 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u41_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u42_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#1868F1;
  text-align:center;
}
#u42 {
  border-width:0px;
  position:absolute;
  left:1333px;
  top:241px;
  width:80px;
  height:21px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#1868F1;
  text-align:center;
}
#u42 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u42_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u43_img {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-3px;
  width:56px;
  height:11px;
}
#u43 {
  border-width:0px;
  position:absolute;
  left:1348px;
  top:272px;
  width:50px;
  height:5px;
  display:flex;
}
#u43 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u43_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u44_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u44 {
  border-width:0px;
  position:absolute;
  left:1420px;
  top:599px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u44 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u44_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u45_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u45 {
  border-width:0px;
  position:absolute;
  left:1202px;
  top:662px;
  width:500px;
  height:1px;
  display:flex;
}
#u45 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u45_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u46_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:center;
}
#u46 {
  border-width:0px;
  position:absolute;
  left:1394px;
  top:655px;
  width:116px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:center;
}
#u46 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 10px 0px 10px;
  box-sizing:border-box;
  width:100%;
}
#u46_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u47_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:163px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u47 {
  border-width:0px;
  position:absolute;
  left:1371px;
  top:696px;
  width:163px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u47 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u47_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u48_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u48 {
  border-width:0px;
  position:absolute;
  left:1202px;
  top:324px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u48 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u48_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u49_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u49 {
  border-width:0px;
  position:absolute;
  left:1202px;
  top:394px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u49 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u49_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u50_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:426px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u50 {
  border-width:0px;
  position:absolute;
  left:1276px;
  top:307px;
  width:426px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u50 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u50_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u51_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u51 {
  border-width:0px;
  position:absolute;
  left:1276px;
  top:377px;
  width:300px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u51 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u51_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u52_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u52 {
  border-width:0px;
  position:absolute;
  left:1586px;
  top:377px;
  width:116px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u52 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u52_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
