﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:1912px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2031_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2031 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  display:flex;
}
#u2031 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2031_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2033_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:884px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:10px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  -webkit-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
}
#u2033 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:200px;
  height:884px;
  display:flex;
}
#u2033 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2033_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2034_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u2034 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u2034 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2034_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2035_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:70px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  -webkit-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
}
#u2035 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:70px;
  display:flex;
}
#u2035 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2035_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2036 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2037_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u2037 {
  border-width:0px;
  position:absolute;
  left:1840px;
  top:20px;
  width:30px;
  height:30px;
  display:flex;
}
#u2037 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2037_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2038_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:4px;
}
#u2038 {
  border-width:0px;
  position:absolute;
  left:1875px;
  top:33px;
  width:7px;
  height:4px;
  display:flex;
  color:#555555;
}
#u2038 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2038_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2039 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2040_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2040 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:26px;
  width:73px;
  height:19px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2040 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2040_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2041_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u2041 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:20px;
  width:30px;
  height:30px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u2041 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2041_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2042 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2043_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:153px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:0px 0px 10px rgba(85, 85, 85, 0.34901960784313724);
  -webkit-box-shadow:0px 0px 10px rgba(85, 85, 85, 0.34901960784313724);
  box-shadow:0px 0px 10px rgba(85, 85, 85, 0.34901960784313724);
}
#u2043 {
  border-width:0px;
  position:absolute;
  left:1762px;
  top:59px;
  width:150px;
  height:153px;
  display:flex;
}
#u2043 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2043_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2044_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:center;
}
#u2044 {
  border-width:0px;
  position:absolute;
  left:1816px;
  top:79px;
  width:42px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:center;
}
#u2044 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2044_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2045_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u2045 {
  border-width:0px;
  position:absolute;
  left:1809px;
  top:134px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u2045 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2045_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2046_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u2046 {
  border-width:0px;
  position:absolute;
  left:1809px;
  top:178px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u2046 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2046_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2047_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:2px;
}
#u2047 {
  border-width:0px;
  position:absolute;
  left:1772px;
  top:113px;
  width:130px;
  height:1px;
  display:flex;
}
#u2047 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2047_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2048_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u2048 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:122px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u2048 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2048_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2049_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u2049 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:174px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u2049 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2049_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2050_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u2050 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:226px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u2050 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2050_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2051_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2051 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:88px;
  width:70px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2051 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2051_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2052_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2052 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:140px;
  width:84px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2052 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2052_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2053_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2053 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:192px;
  width:129px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2053 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2053_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2054_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2054 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:244px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2054 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2054_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2055_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2055 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:241px;
  width:20px;
  height:20px;
  display:flex;
}
#u2055 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2055_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2056_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2056 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:85px;
  width:20px;
  height:20px;
  display:flex;
}
#u2056 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2056_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2057_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2057 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:137px;
  width:20px;
  height:20px;
  display:flex;
}
#u2057 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2057_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2058_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2058 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:189px;
  width:20px;
  height:20px;
  display:flex;
}
#u2058 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2058_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2059_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1692px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2059 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:80px;
  width:1692px;
  height:60px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2059 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2059_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2060_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:3px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2060 {
  border-width:0px;
  position:absolute;
  left:520px;
  top:137px;
  width:60px;
  height:3px;
  display:flex;
}
#u2060 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2060_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2061_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u2061 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:102px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u2061 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2061_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2062_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u2062 {
  border-width:0px;
  position:absolute;
  left:394px;
  top:102px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u2062 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2062_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2063_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
  text-align:center;
}
#u2063 {
  border-width:0px;
  position:absolute;
  left:518px;
  top:102px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
  text-align:center;
}
#u2063 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2063_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2064_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u2064 {
  border-width:0px;
  position:absolute;
  left:642px;
  top:102px;
  width:80px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u2064 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2064_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2065_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1692px;
  height:100px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2065 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:150px;
  width:1692px;
  height:100px;
  display:flex;
  opacity:0.9;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2065 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2065_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2066_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2066 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:188px;
  width:31px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2066 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2066_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2067_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:294px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2067 {
  border-width:0px;
  position:absolute;
  left:261px;
  top:175px;
  width:294px;
  height:40px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2067 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2067_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2068_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:4px;
}
#u2068 {
  border-width:0px;
  position:absolute;
  left:538px;
  top:193px;
  width:7px;
  height:4px;
  display:flex;
}
#u2068 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2068_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2069_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2069 {
  border-width:0px;
  position:absolute;
  left:595px;
  top:188px;
  width:70px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2069 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2069_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2070_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:14px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2070 {
  border-width:0px;
  position:absolute;
  left:670px;
  top:188px;
  width:300px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2070 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2070_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2071_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:14px;
  background:inherit;
  background-color:rgba(51, 51, 51, 1);
  border:none;
  border-radius:10px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2071 {
  border-width:0px;
  position:absolute;
  left:670px;
  top:188px;
  width:131px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2071 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2071_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2072_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2072 {
  border-width:0px;
  position:absolute;
  left:791px;
  top:185px;
  width:20px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2072 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2072_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2073_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
}
#u2073 {
  border-width:0px;
  position:absolute;
  left:670px;
  top:212px;
  width:38px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
}
#u2073 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2073_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2074_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
  text-align:center;
}
#u2074 {
  border-width:0px;
  position:absolute;
  left:754px;
  top:212px;
  width:94px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
  text-align:center;
}
#u2074 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2074_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2075_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:46px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
  text-align:right;
}
#u2075 {
  border-width:0px;
  position:absolute;
  left:924px;
  top:212px;
  width:46px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
  text-align:right;
}
#u2075 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2075_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2076_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:24px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.4980392156862745);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2076 {
  border-width:0px;
  position:absolute;
  left:975px;
  top:183px;
  width:60px;
  height:24px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2076 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2076_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2077_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u2077 {
  border-width:0px;
  position:absolute;
  left:986px;
  top:188px;
  width:38px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u2077 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2077_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2078_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2078 {
  border-width:0px;
  position:absolute;
  left:1075px;
  top:188px;
  width:154px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2078 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2078_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2079_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:14px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2079 {
  border-width:0px;
  position:absolute;
  left:1234px;
  top:188px;
  width:300px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2079 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2079_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2080_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:14px;
  background:inherit;
  background-color:rgba(51, 51, 51, 1);
  border:none;
  border-radius:10px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2080 {
  border-width:0px;
  position:absolute;
  left:1234px;
  top:188px;
  width:131px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2080 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2080_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2081_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2081 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:185px;
  width:20px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2081 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2081_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2082_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
}
#u2082 {
  border-width:0px;
  position:absolute;
  left:1234px;
  top:212px;
  width:24px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
}
#u2082 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2082_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2083_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
  text-align:center;
}
#u2083 {
  border-width:0px;
  position:absolute;
  left:1325px;
  top:212px;
  width:80px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
  text-align:center;
}
#u2083 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2083_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2084_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
  text-align:right;
}
#u2084 {
  border-width:0px;
  position:absolute;
  left:1515px;
  top:212px;
  width:19px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
  text-align:right;
}
#u2084 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2084_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2085_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:24px;
  background:inherit;
  background-color:rgba(215, 215, 215, 0.4980392156862745);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2085 {
  border-width:0px;
  position:absolute;
  left:1539px;
  top:183px;
  width:60px;
  height:24px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2085 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2085_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2086_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u2086 {
  border-width:0px;
  position:absolute;
  left:1550px;
  top:188px;
  width:38px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u2086 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2086_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2087_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:900px;
  height:503px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2087 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:260px;
  width:900px;
  height:503px;
  display:flex;
  opacity:0.9;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2087 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2087_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2088_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2088 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:275px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2088 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2088_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2089_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:5px;
  height:23px;
}
#u2089 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:273px;
  width:2px;
  height:20px;
  display:flex;
}
#u2089 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2089_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2090 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2091_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:831px;
  height:2px;
}
#u2091 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:682px;
  width:830px;
  height:1px;
  display:flex;
}
#u2091 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2091_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2092_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:831px;
  height:2px;
}
#u2092 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:592px;
  width:830px;
  height:1px;
  display:flex;
}
#u2092 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2092_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2093_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:831px;
  height:2px;
}
#u2093 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:547px;
  width:830px;
  height:1px;
  display:flex;
}
#u2093 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2093_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2094_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:831px;
  height:2px;
}
#u2094 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:501px;
  width:830px;
  height:1px;
  display:flex;
}
#u2094 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2094_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2095_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:831px;
  height:2px;
}
#u2095 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:456px;
  width:830px;
  height:1px;
  display:flex;
}
#u2095 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2095_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2096_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2096 {
  border-width:0px;
  position:absolute;
  left:248px;
  top:673px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2096 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2096_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2097_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2097 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:538px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2097 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2097_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2098_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2098 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:447px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2098 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2098_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2099_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2099 {
  border-width:0px;
  position:absolute;
  left:335px;
  top:683px;
  width:1px;
  height:5px;
  display:flex;
}
#u2099 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2099_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2100_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2100 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:693px;
  width:27px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2100 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2100_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2101_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2101 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:583px;
  width:20px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2101 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2101_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2102_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2102 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:492px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2102 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2102_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2103_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:831px;
  height:2px;
}
#u2103 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:637px;
  width:830px;
  height:1px;
  display:flex;
}
#u2103 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2103_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2104_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2104 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:628px;
  width:20px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2104 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2104_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2105_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2105 {
  border-width:0px;
  position:absolute;
  left:507px;
  top:683px;
  width:1px;
  height:5px;
  display:flex;
}
#u2105 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2105_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2106_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2106 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:693px;
  width:27px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2106 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2106_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2107_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2107 {
  border-width:0px;
  position:absolute;
  left:680px;
  top:683px;
  width:1px;
  height:5px;
  display:flex;
}
#u2107 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2107_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2108_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2108 {
  border-width:0px;
  position:absolute;
  left:667px;
  top:693px;
  width:27px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2108 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2108_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2109_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2109 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:683px;
  width:1px;
  height:5px;
  display:flex;
}
#u2109 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2109_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2110_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2110 {
  border-width:0px;
  position:absolute;
  left:839px;
  top:693px;
  width:27px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2110 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2110_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2111_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2111 {
  border-width:0px;
  position:absolute;
  left:1024px;
  top:683px;
  width:1px;
  height:5px;
  display:flex;
}
#u2111 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2111_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2112_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2112 {
  border-width:0px;
  position:absolute;
  left:1011px;
  top:693px;
  width:27px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2112 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2112_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2113_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:217px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2113 {
  border-width:0px;
  position:absolute;
  left:295px;
  top:465px;
  width:40px;
  height:217px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2113 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2113_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2114_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:831px;
  height:2px;
}
#u2114 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:411px;
  width:830px;
  height:1px;
  display:flex;
}
#u2114 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2114_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2115_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2115 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:402px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2115 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2115_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2116_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:171px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2116 {
  border-width:0px;
  position:absolute;
  left:336px;
  top:511px;
  width:40px;
  height:171px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2116 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2116_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2117_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:250px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2117 {
  border-width:0px;
  position:absolute;
  left:467px;
  top:432px;
  width:40px;
  height:250px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2117 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2117_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2118_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:216px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2118 {
  border-width:0px;
  position:absolute;
  left:508px;
  top:466px;
  width:40px;
  height:216px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2118 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2118_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2119_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:197px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2119 {
  border-width:0px;
  position:absolute;
  left:640px;
  top:485px;
  width:40px;
  height:197px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2119 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2119_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2120_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:171px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2120 {
  border-width:0px;
  position:absolute;
  left:681px;
  top:511px;
  width:40px;
  height:171px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2120 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2120_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2121_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:199px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2121 {
  border-width:0px;
  position:absolute;
  left:812px;
  top:483px;
  width:40px;
  height:199px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2121 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2121_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2122_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:185px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2122 {
  border-width:0px;
  position:absolute;
  left:853px;
  top:497px;
  width:40px;
  height:185px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2122 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2122_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2123_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:222px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2123 {
  border-width:0px;
  position:absolute;
  left:984px;
  top:460px;
  width:40px;
  height:222px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2123 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2123_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2124_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:199px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2124 {
  border-width:0px;
  position:absolute;
  left:1025px;
  top:483px;
  width:40px;
  height:199px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2124 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2124_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2125 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2126 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2127 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2128_img {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-10px;
  width:180px;
  height:79px;
}
#u2128 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:432px;
  width:160px;
  height:59px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2128 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2128_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2129_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2129 {
  border-width:0px;
  position:absolute;
  left:522px;
  top:459px;
  width:68px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2129 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2129_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2130_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u2130 {
  border-width:0px;
  position:absolute;
  left:522px;
  top:442px;
  width:27px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u2130 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2130_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2131_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u2131 {
  border-width:0px;
  position:absolute;
  left:527px;
  top:467px;
  width:6px;
  height:6px;
  display:flex;
}
#u2131 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2131_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2132_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u2132 {
  border-width:0px;
  position:absolute;
  left:538px;
  top:464px;
  width:47px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u2132 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2132_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2133_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2133 {
  border-width:0px;
  position:absolute;
  left:594px;
  top:459px;
  width:68px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2133 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2133_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2134_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u2134 {
  border-width:0px;
  position:absolute;
  left:599px;
  top:467px;
  width:6px;
  height:6px;
  display:flex;
}
#u2134 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2134_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2135_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#2FC25B;
  line-height:12px;
}
#u2135 {
  border-width:0px;
  position:absolute;
  left:610px;
  top:464px;
  width:47px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#2FC25B;
  line-height:12px;
}
#u2135 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2135_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2136_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2136 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:372px;
  width:24px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2136 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2136_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2137_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-style:normal;
}
#u2137 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:321px;
  width:132px;
  height:21px;
  display:flex;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-style:normal;
}
#u2137 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2137_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2138_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-style:normal;
}
#u2138 {
  border-width:0px;
  position:absolute;
  left:387px;
  top:321px;
  width:75px;
  height:21px;
  display:flex;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-style:normal;
}
#u2138 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2138_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2139_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2139 {
  border-width:0px;
  position:absolute;
  left:523px;
  top:732px;
  width:10px;
  height:10px;
  display:flex;
}
#u2139 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2139_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2140_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2140 {
  border-width:0px;
  position:absolute;
  left:538px;
  top:731px;
  width:84px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2140 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2140_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2141_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u2141 {
  border-width:0px;
  position:absolute;
  left:662px;
  top:732px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u2141 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2141_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2142_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2142 {
  border-width:0px;
  position:absolute;
  left:677px;
  top:731px;
  width:120px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2142 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2142_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2143_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:782px;
  height:441px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2143 {
  border-width:0px;
  position:absolute;
  left:1120px;
  top:260px;
  width:782px;
  height:441px;
  display:flex;
  opacity:0.9;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2143 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2143_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2144_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2144 {
  border-width:0px;
  position:absolute;
  left:1135px;
  top:275px;
  width:112px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2144 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2144_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2145_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:5px;
  height:23px;
}
#u2145 {
  border-width:0px;
  position:absolute;
  left:1120px;
  top:273px;
  width:2px;
  height:20px;
  display:flex;
}
#u2145 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2145_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2146 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2147_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u2147 {
  border-width:0px;
  position:absolute;
  left:1135px;
  top:321px;
  width:70px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u2147 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2147_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2148_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-style:normal;
}
#u2148 {
  border-width:0px;
  position:absolute;
  left:1135px;
  top:345px;
  width:60px;
  height:25px;
  display:flex;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-style:normal;
}
#u2148 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2148_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2149 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2150_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u2150 {
  border-width:0px;
  position:absolute;
  left:1245px;
  top:321px;
  width:42px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u2150 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2150_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2151_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-style:normal;
}
#u2151 {
  border-width:0px;
  position:absolute;
  left:1245px;
  top:345px;
  width:117px;
  height:25px;
  display:flex;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-style:normal;
}
#u2151 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2151_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2152 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2153_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u2153 {
  border-width:0px;
  position:absolute;
  left:1402px;
  top:321px;
  width:70px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
}
#u2153 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2153_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2154_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-style:normal;
}
#u2154 {
  border-width:0px;
  position:absolute;
  left:1402px;
  top:345px;
  width:63px;
  height:25px;
  display:flex;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-style:normal;
}
#u2154 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2154_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2155 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2156_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:713px;
  height:2px;
}
#u2156 {
  border-width:0px;
  position:absolute;
  left:1175px;
  top:620px;
  width:712px;
  height:1px;
  display:flex;
}
#u2156 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2156_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2157_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:713px;
  height:2px;
}
#u2157 {
  border-width:0px;
  position:absolute;
  left:1175px;
  top:530px;
  width:712px;
  height:1px;
  display:flex;
}
#u2157 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2157_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2158_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:713px;
  height:2px;
}
#u2158 {
  border-width:0px;
  position:absolute;
  left:1175px;
  top:485px;
  width:712px;
  height:1px;
  display:flex;
}
#u2158 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2158_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2159_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:713px;
  height:2px;
}
#u2159 {
  border-width:0px;
  position:absolute;
  left:1175px;
  top:439px;
  width:712px;
  height:1px;
  display:flex;
}
#u2159 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2159_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2160_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2160 {
  border-width:0px;
  position:absolute;
  left:1158px;
  top:611px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2160 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2160_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2161_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2161 {
  border-width:0px;
  position:absolute;
  left:1135px;
  top:476px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2161 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2161_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2162_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2162 {
  border-width:0px;
  position:absolute;
  left:1315px;
  top:621px;
  width:1px;
  height:5px;
  display:flex;
}
#u2162 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2162_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2163_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2163 {
  border-width:0px;
  position:absolute;
  left:1302px;
  top:631px;
  width:27px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2163 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2163_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2164_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2164 {
  border-width:0px;
  position:absolute;
  left:1145px;
  top:521px;
  width:20px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2164 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2164_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2165_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2165 {
  border-width:0px;
  position:absolute;
  left:1135px;
  top:430px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2165 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2165_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2166_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:713px;
  height:2px;
}
#u2166 {
  border-width:0px;
  position:absolute;
  left:1175px;
  top:575px;
  width:712px;
  height:1px;
  display:flex;
}
#u2166 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2166_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2167_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2167 {
  border-width:0px;
  position:absolute;
  left:1145px;
  top:566px;
  width:20px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2167 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2167_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2168_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2168 {
  border-width:0px;
  position:absolute;
  left:1531px;
  top:621px;
  width:1px;
  height:5px;
  display:flex;
}
#u2168 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2168_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2169_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2169 {
  border-width:0px;
  position:absolute;
  left:1518px;
  top:631px;
  width:27px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2169 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2169_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2170_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2170 {
  border-width:0px;
  position:absolute;
  left:1746px;
  top:621px;
  width:1px;
  height:5px;
  display:flex;
}
#u2170 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2170_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2171_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2171 {
  border-width:0px;
  position:absolute;
  left:1733px;
  top:631px;
  width:27px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2171 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2171_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2172_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:69px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2172 {
  border-width:0px;
  position:absolute;
  left:1275px;
  top:551px;
  width:40px;
  height:69px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2172 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2172_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2173_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:54px;
  background:inherit;
  background-color:rgba(166, 203, 255, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2173 {
  border-width:0px;
  position:absolute;
  left:1316px;
  top:566px;
  width:40px;
  height:54px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2173 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2173_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2174_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:164px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2174 {
  border-width:0px;
  position:absolute;
  left:1491px;
  top:456px;
  width:40px;
  height:164px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2174 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2174_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2175_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:119px;
  background:inherit;
  background-color:rgba(166, 203, 255, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2175 {
  border-width:0px;
  position:absolute;
  left:1532px;
  top:501px;
  width:40px;
  height:119px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2175 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2175_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2176_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:157px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2176 {
  border-width:0px;
  position:absolute;
  left:1706px;
  top:463px;
  width:40px;
  height:157px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2176 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2176_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2177_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:113px;
  background:inherit;
  background-color:rgba(166, 203, 255, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2177 {
  border-width:0px;
  position:absolute;
  left:1747px;
  top:507px;
  width:40px;
  height:113px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2177 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2177_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2178 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2179 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2180 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2181_img {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-10px;
  width:188px;
  height:79px;
}
#u2181 {
  border-width:0px;
  position:absolute;
  left:1536px;
  top:456px;
  width:168px;
  height:59px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2181 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2181_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2182_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2182 {
  border-width:0px;
  position:absolute;
  left:1546px;
  top:483px;
  width:75px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2182 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2182_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2183_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u2183 {
  border-width:0px;
  position:absolute;
  left:1546px;
  top:466px;
  width:27px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u2183 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2183_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2184_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u2184 {
  border-width:0px;
  position:absolute;
  left:1551px;
  top:491px;
  width:6px;
  height:6px;
  display:flex;
}
#u2184 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2184_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2185_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u2185 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:488px;
  width:54px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u2185 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2185_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2186_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2186 {
  border-width:0px;
  position:absolute;
  left:1626px;
  top:483px;
  width:68px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2186 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2186_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2187_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u2187 {
  border-width:0px;
  position:absolute;
  left:1631px;
  top:491px;
  width:6px;
  height:6px;
  display:flex;
}
#u2187 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2187_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2188_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#A6CBFF;
  line-height:12px;
}
#u2188 {
  border-width:0px;
  position:absolute;
  left:1642px;
  top:488px;
  width:47px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#A6CBFF;
  line-height:12px;
}
#u2188 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2188_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2189_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2189 {
  border-width:0px;
  position:absolute;
  left:1141px;
  top:400px;
  width:24px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2189 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2189_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2190_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2190 {
  border-width:0px;
  position:absolute;
  left:1374px;
  top:670px;
  width:10px;
  height:10px;
  display:flex;
}
#u2190 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2190_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2191_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2191 {
  border-width:0px;
  position:absolute;
  left:1389px;
  top:669px;
  width:84px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2191 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2191_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2192_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(166, 203, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u2192 {
  border-width:0px;
  position:absolute;
  left:1513px;
  top:670px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u2192 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2192_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2193_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2193 {
  border-width:0px;
  position:absolute;
  left:1528px;
  top:669px;
  width:120px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2193 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2193_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2194_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:68px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:20px;
}
#u2194 {
  border-width:0px;
  position:absolute;
  left:1649px;
  top:161px;
  width:160px;
  height:68px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:20px;
}
#u2194 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2194_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2195 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2196_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:40px;
}
#u2196 {
  border-width:0px;
  position:absolute;
  left:912px;
  top:270px;
  width:198px;
  height:40px;
  display:flex;
}
#u2196 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2196_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2197_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u2197 {
  border-width:0px;
  position:absolute;
  left:927px;
  top:283px;
  width:112px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u2197 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2197_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2198_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u2198 {
  border-width:0px;
  position:absolute;
  left:1049px;
  top:275px;
  width:30px;
  height:30px;
  display:flex;
}
#u2198 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2198_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2199_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:20px;
}
#u2199 {
  border-width:0px;
  position:absolute;
  left:1055px;
  top:280px;
  width:18px;
  height:20px;
  display:flex;
}
#u2199 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2199_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2200_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:10px;
}
#u2200 {
  border-width:0px;
  position:absolute;
  left:1089px;
  top:285px;
  width:6px;
  height:10px;
  display:flex;
}
#u2200 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2200_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2201 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2202_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:40px;
}
#u2202 {
  border-width:0px;
  position:absolute;
  left:1704px;
  top:270px;
  width:198px;
  height:40px;
  display:flex;
}
#u2202 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2202_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2203_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u2203 {
  border-width:0px;
  position:absolute;
  left:1719px;
  top:283px;
  width:112px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u2203 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2203_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2204_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u2204 {
  border-width:0px;
  position:absolute;
  left:1841px;
  top:275px;
  width:30px;
  height:30px;
  display:flex;
}
#u2204 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2204_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2205_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:20px;
}
#u2205 {
  border-width:0px;
  position:absolute;
  left:1847px;
  top:280px;
  width:18px;
  height:20px;
  display:flex;
}
#u2205 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2205_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2206_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:10px;
}
#u2206 {
  border-width:0px;
  position:absolute;
  left:1881px;
  top:285px;
  width:6px;
  height:10px;
  display:flex;
}
#u2206 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2206_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
