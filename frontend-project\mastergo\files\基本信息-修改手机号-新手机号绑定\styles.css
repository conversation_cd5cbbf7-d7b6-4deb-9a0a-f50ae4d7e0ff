﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:1912px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u3222_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  background:inherit;
  background-color:rgba(51, 51, 51, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3222 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  display:flex;
  opacity:0.75;
}
#u3222 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3222_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3223_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:311px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3223 {
  border-width:0px;
  position:absolute;
  left:706px;
  top:240px;
  width:500px;
  height:311px;
  display:flex;
}
#u3223 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3223_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3224_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u3224 {
  border-width:0px;
  position:absolute;
  left:726px;
  top:260px;
  width:96px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u3224 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3224_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3225_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u3225 {
  border-width:0px;
  position:absolute;
  left:1166px;
  top:258px;
  width:20px;
  height:20px;
  display:flex;
}
#u3225 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3225_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3226_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3226 {
  border-width:0px;
  position:absolute;
  left:746px;
  top:324px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3226 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3226_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3227_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(127, 127, 127, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
  text-align:left;
}
#u3227 {
  border-width:0px;
  position:absolute;
  left:812px;
  top:306px;
  width:354px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
  text-align:left;
}
#u3227 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u3227_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3228_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:25px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u3228 {
  border-width:0px;
  position:absolute;
  left:856px;
  top:471px;
  width:200px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u3228 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3228_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3229_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:right;
}
#u3229 {
  border-width:0px;
  position:absolute;
  left:746px;
  top:394px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:right;
}
#u3229 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3229_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3230_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(127, 127, 127, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
  text-align:left;
}
#u3230 {
  border-width:0px;
  position:absolute;
  left:812px;
  top:376px;
  width:228px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
  text-align:left;
}
#u3230 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u3230_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3231_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(127, 127, 127, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3231 {
  border-width:0px;
  position:absolute;
  left:1050px;
  top:376px;
  width:116px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3231 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3231_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
