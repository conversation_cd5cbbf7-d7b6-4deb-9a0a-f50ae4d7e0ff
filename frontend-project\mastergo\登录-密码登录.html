﻿<!DOCTYPE html>
<html>
  <head>
    <title>登录-密码登录</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/登录-密码登录/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/登录-密码登录/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u0" class="ax_default box_2">
        <div id="u0_div" class=""></div>
        <div id="u0_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1" class="ax_default _图片">
        <img id="u1_img" class="img " src="images/登录-密码登录/u1.png"/>
        <div id="u1_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2" class="ax_default box_2">
        <div id="u2_div" class=""></div>
        <div id="u2_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u3" class="ax_default" data-left="40" data-top="30" data-width="195" data-height="40" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u4" class="ax_default _三级标题">
          <div id="u4_div" class=""></div>
          <div id="u4_text" class="text ">
            <p><span>rev-REITs平台</span></p>
          </div>
        </div>

        <!-- Unnamed (圆形) -->
        <div id="u5" class="ax_default ellipse">
          <img id="u5_img" class="img " src="images/登录-密码登录/u5.svg"/>
          <div id="u5_text" class="text ">
            <p><span>logo</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6" class="ax_default box_2">
        <div id="u6_div" class=""></div>
        <div id="u6_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u7" class="ax_default _三级标题">
        <div id="u7_div" class=""></div>
        <div id="u7_text" class="text ">
          <p><span>密码登录</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u8" class="ax_default" data-left="1547" data-top="243" data-width="155" data-height="16" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u9" class="ax_default _三级标题">
          <div id="u9_div" class=""></div>
          <div id="u9_text" class="text ">
            <p><span>还没有账号，去</span><span style="color:#1868F1;">注册</span></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u10" class="ax_default icon">
          <img id="u10_img" class="img " src="images/登录-密码登录/u10.svg"/>
          <div id="u10_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u11" class="ax_default _三级标题">
        <div id="u11_div" class=""></div>
        <div id="u11_text" class="text ">
          <p><span>用&nbsp; 户&nbsp; 名</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u12" class="ax_default box_2">
        <div id="u12_div" class=""></div>
        <div id="u12_text" class="text ">
          <p><span>请输入用户名/手机号</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u13" class="ax_default _三级标题">
        <div id="u13_div" class=""></div>
        <div id="u13_text" class="text ">
          <p><span>密&nbsp; &nbsp; &nbsp; &nbsp; 码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u14" class="ax_default box_2">
        <div id="u14_div" class=""></div>
        <div id="u14_text" class="text ">
          <p><span>请输入登录密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u15" class="ax_default box_2">
        <div id="u15_div" class=""></div>
        <div id="u15_text" class="text ">
          <p><span>登录</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u16" class="ax_default" data-left="1202" data-top="507" data-width="268" data-height="12" layer-opacity="1">

        <!-- Unnamed (复选框) -->
        <div id="u17" class="ax_default checkbox">
          <label id="u17_input_label" for="u17_input" style="position: absolute; left: 0px;">
            <img id="u17_img" class="img " src="images/登录-密码登录/u17.svg"/>
            <div id="u17_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </label>
          <input id="u17_input" type="checkbox" value="checkbox"/>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u18" class="ax_default _三级标题">
          <div id="u18_div" class=""></div>
          <div id="u18_text" class="text ">
            <p><span>阅读并同意</span><span style="color:#1868F1;">用户协议</span><span>、</span><span style="color:#1868F1;">隐私声明</span><span>、</span><span style="color:#1868F1;">产品使用条款</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u19" class="ax_default _图片">
        <img id="u19_img" class="img " src="images/登录-密码登录/u19.png"/>
        <div id="u19_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u20" class="ax_default _三级标题">
        <div id="u20_div" class=""></div>
        <div id="u20_text" class="text ">
          <p><span>短信登录</span></p>
        </div>
      </div>

      <!-- Unnamed (直线) -->
      <div id="u21" class="ax_default line">
        <img id="u21_img" class="img " src="images/登录-密码登录/u21.svg"/>
        <div id="u21_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u22" class="ax_default _三级标题">
        <div id="u22_div" class=""></div>
        <div id="u22_text" class="text ">
          <p><span>忘记密码</span></p>
        </div>
      </div>

      <!-- Unnamed (直线) -->
      <div id="u23" class="ax_default line">
        <img id="u23_img" class="img " src="images/登录-密码登录/u23.svg"/>
        <div id="u23_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u24" class="ax_default _三级标题">
        <div id="u24_div" class=""></div>
        <div id="u24_text" class="text ">
          <p><span>其他登录方式</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u25" class="ax_default _三级标题">
        <div id="u25_div" class=""></div>
        <div id="u25_text" class="text ">
          <p><span>中国REITs论坛账号登录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u26" class="ax_default sticky_1">
        <div id="u26_div" class=""></div>
        <div id="u26_text" class="text ">
          <p><span>1、确认是否必须登录才能进入首页</span></p><p><span>2、点击“中国REITs论坛账号登录”跳转对应网站验证，需对接</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
