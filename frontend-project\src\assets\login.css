/* 登录页面专用样式 - 基于 mastergo 设计稿 */

/* 主背景 - 精确还原 mastergo 设计 */
.login-bg {
  background-color: rgba(242, 242, 242, 1); /* u0 背景色 */
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  width: 100%;
  margin: 0;
  padding: 0;
}

/* 背景遮罩层 - 对应 u2 */
.login-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.4980392156862745); /* u2 半透明白色遮罩 */
  z-index: 1;
}

/* Logo 样式 - 精确对应 u5 */
.login-logo {
  width: 40px;
  height: 40px;
  background: #1868F1;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  position: relative;
  z-index: 10;
}

/* 品牌标题 - 精确对应 u4 */
.brand-title {
  font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 24px;
  color: #1868F1;
  white-space: nowrap;
  margin-left: 10px;
}

/* 登录卡片 - 精确对应 u6 */
.login-card {
  background: rgba(255, 255, 255, 0.8); /* u6 背景色 */
  border-radius: 20px; /* u6 圆角 */
  width: 560px; /* u6 宽度 */
  height: 551px; /* u6 高度 */
  position: relative;
  z-index: 10;
  padding: 0;
  box-shadow: none; /* mastergo 设计中没有阴影 */
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}

/* 标签切换 - 精确对应 u7, u20 */
.tab-button {
  font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 20px;
  color: #666666;
  text-align: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  width: 80px;
  height: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s ease;
}

.tab-button.active {
  color: #1868F1; /* u7 激活状态颜色 */
}

.tab-button:not(.active):hover {
  color: #333;
}

/* 标签下划线 - 对应 u21, u23 */
.tab-indicator {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 2px;
  background: #1868F1;
}

/* 表单标签 - 精确对应 u11, u13 */
.form-label {
  font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 16px;
  color: #333333;
  text-align: right;
  white-space: nowrap;
  width: 64px;
  height: 16px;
  display: block;
  margin-bottom: 8px;
}

/* 输入框样式 - 精确对应 u12, u14 */
.login-input {
  width: 426px;
  height: 50px;
  background-color: rgba(255, 255, 255, 1);
  border: 1px solid rgba(215, 215, 215, 1);
  border-radius: 5px;
  padding: 0 10px;
  font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 16px;
  color: #333333;
  text-align: left;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.login-input:focus {
  border-color: #1868F1;
  outline: none;
}

.login-input::placeholder {
  color: #AAAAAA; /* u12, u14 占位符颜色 */
}

/* 登录按钮 - 精确对应 u15 */
.login-button {
  width: 500px;
  height: 50px;
  background-color: rgba(24, 104, 241, 1); /* u15 背景色 */
  border: none;
  border-radius: 25px; /* u15 圆角 */
  color: #FFFFFF;
  font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  transition: background-color 0.3s ease;
}

.login-button:hover {
  background-color: #1557d6;
}

.login-button:active {
  background-color: #1446c0;
}

.login-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* 复选框样式 - 精确对应 u17, u18 */
.agreement-checkbox {
  font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #333333;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.agreement-checkbox input[type="checkbox"] {
  width: 12px;
  height: 12px;
  margin-right: 6px;
}

.agreement-checkbox a {
  color: #1868F1;
  text-decoration: none;
}

.agreement-checkbox a:hover {
  text-decoration: underline;
}

/* 链接样式 - 精确对应设计稿 */
.login-link {
  color: #1868F1;
  text-decoration: none;
  font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight: 400;
  font-style: normal;
  transition: color 0.3s ease;
}

.login-link:hover {
  color: #1557d6;
  text-decoration: underline;
}

/* 注册链接 - 对应 u9 */
.register-link {
  font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 16px;
  text-align: right;
  white-space: nowrap;
}

/* 忘记密码链接 - 对应 u22 */
.forgot-password {
  font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 16px;
  color: #333333;
  text-align: center;
}

/* 精确布局定位 - 基于 mastergo 坐标 */
.login-container {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 顶部 Logo 区域定位 - 对应 u3 */
.header-logo {
  position: absolute;
  left: 40px;
  top: 30px;
  width: 195px;
  height: 40px;
  display: flex;
  align-items: center;
  z-index: 20;
}

/* 注册链接定位 - 对应 u8 */
.header-register {
  position: absolute;
  right: 40px;
  top: 43px;
  display: flex;
  align-items: center;
  z-index: 20;
}

/* 登录卡片定位 - 对应 u6 */
.login-card-positioned {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

/* 分割线 - 对应 u23, u24 */
.divider {
  position: relative;
  width: 500px;
  height: 1px;
  background: #e0e0e0;
  margin: 30px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.divider span {
  background: white;
  padding: 0 10px;
  font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 16px;
  color: #AAAAAA;
  text-align: center;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.3;
  animation: float 6s ease-in-out infinite;
}

.bg-decoration:nth-child(1) {
  top: 10%;
  left: 10%;
  width: 200px;
  height: 200px;
  background: linear-gradient(45deg, #1868F1, #4285f4);
  animation-delay: 0s;
}

.bg-decoration:nth-child(2) {
  top: 20%;
  right: 10%;
  width: 250px;
  height: 250px;
  background: linear-gradient(45deg, #9c27b0, #e91e63);
  animation-delay: 2s;
}

.bg-decoration:nth-child(3) {
  bottom: 10%;
  left: 20%;
  width: 300px;
  height: 300px;
  background: linear-gradient(45deg, #ff9800, #ff5722);
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-20px) scale(1.05);
  }
}

/* 响应式设计 - 移动端优化 */

/* 平板设备 (768px - 1024px) */
@media (max-width: 1024px) and (min-width: 769px) {
  .login-card {
    max-width: 450px;
    padding: 35px;
  }

  .bg-decoration {
    opacity: 0.2;
  }
}

/* 移动端设备 (最大宽度 768px) */
@media (max-width: 768px) {
  .login-bg {
    padding: 0;
    background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
  }

  .login-card {
    margin: 10px;
    padding: 24px 20px;
    max-width: none;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  .login-logo {
    width: 32px;
    height: 32px;
    font-size: 10px;
  }

  /* 隐藏背景装饰，提升性能 */
  .bg-decoration {
    display: none;
  }

  /* 顶部Logo区域移动端优化 */
  .mobile-header {
    position: absolute;
    top: 15px;
    left: 15px;
    right: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 10;
  }

  .mobile-header .brand {
    display: flex;
    align-items: center;
    font-size: 16px;
  }

  .mobile-header .register-link {
    font-size: 13px;
  }

  /* 表单标签移动端优化 */
  .form-label {
    font-size: 14px;
    margin-bottom: 6px;
    letter-spacing: 1px;
  }

  /* 输入框移动端优化 */
  .login-input {
    height: 44px;
    font-size: 16px; /* 防止iOS缩放 */
    padding: 0 14px;
    border-radius: 6px;
  }

  /* 按钮移动端优化 */
  .login-button {
    height: 44px;
    font-size: 16px;
    border-radius: 6px;
    touch-action: manipulation; /* 优化触摸体验 */
  }

  /* 标签切换移动端优化 */
  .tab-button {
    padding: 10px 0;
    font-size: 15px;
    touch-action: manipulation;
  }

  /* 复选框区域移动端优化 */
  .agreement-checkbox {
    font-size: 13px;
    line-height: 1.4;
  }

  /* 链接移动端优化 */
  .login-link {
    font-size: 13px;
    padding: 4px; /* 增加触摸区域 */
  }

  /* 分割线移动端优化 */
  .divider {
    margin: 20px 0;
  }

  .divider span {
    font-size: 13px;
    padding: 0 12px;
  }
}

/* 小屏手机设备 (最大宽度 480px) */
@media (max-width: 480px) {
  .login-card {
    margin: 8px;
    padding: 20px 16px;
    border-radius: 10px;
  }

  .mobile-header {
    top: 12px;
    left: 12px;
    right: 12px;
  }

  .mobile-header .brand {
    font-size: 15px;
  }

  .mobile-header .register-link {
    font-size: 12px;
  }

  .login-logo {
    width: 28px;
    height: 28px;
    font-size: 9px;
    margin-right: 8px;
  }

  .tab-button {
    font-size: 14px;
    padding: 8px 0;
  }

  .form-label {
    font-size: 13px;
    margin-bottom: 5px;
  }

  .login-input {
    height: 42px;
    font-size: 16px;
    padding: 0 12px;
  }

  .login-button {
    height: 42px;
    font-size: 15px;
  }

  .agreement-checkbox {
    font-size: 12px;
  }

  .login-link {
    font-size: 12px;
  }
}

/* 横屏模式优化 */
@media (max-height: 600px) and (orientation: landscape) {
  .login-bg {
    padding: 10px 0;
  }

  .login-card {
    margin: 10px auto;
    max-height: 90vh;
    overflow-y: auto;
  }

  .mobile-header {
    position: relative;
    top: 0;
    margin-bottom: 15px;
  }

  .bg-decoration {
    display: none;
  }
}

/* 超小屏设备 (最大宽度 320px) */
@media (max-width: 320px) {
  .login-card {
    margin: 5px;
    padding: 16px 12px;
  }

  .mobile-header .brand {
    font-size: 14px;
  }

  .login-logo {
    width: 24px;
    height: 24px;
    font-size: 8px;
    margin-right: 6px;
  }

  .tab-button {
    font-size: 13px;
  }

  .form-label {
    font-size: 12px;
    letter-spacing: 0.5px;
  }

  .login-input {
    height: 40px;
    font-size: 15px;
    padding: 0 10px;
  }

  .login-button {
    height: 40px;
    font-size: 14px;
  }
}

/* 加载动画 */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 移动端专用优化 */

/* 防止iOS Safari缩放 */
@media screen and (max-width: 768px) {
  input[type="text"],
  input[type="password"],
  input[type="email"],
  input[type="tel"] {
    font-size: 16px !important; /* 防止iOS自动缩放 */
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .login-button:hover {
    background: #1868F1; /* 移除hover效果 */
    transform: none;
  }

  .tab-button:hover {
    color: inherit; /* 移除hover效果 */
  }

  .login-link:hover {
    text-decoration: none; /* 移除hover效果 */
  }

  /* 增加触摸目标大小 */
  .tab-button {
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .login-link {
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    padding: 8px;
  }
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .login-logo {
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .login-card {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
  }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  .login-bg {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  }

  .login-card {
    background: rgba(30, 30, 30, 0.95);
    color: #ffffff;
  }

  .form-label {
    color: #e0e0e0;
  }

  .login-input {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: #ffffff;
  }

  .login-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  .agreement-checkbox {
    color: #b0b0b0;
  }

  .divider::before,
  .divider::after {
    background: rgba(255, 255, 255, 0.2);
  }

  .divider span {
    color: #b0b0b0;
  }
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .bg-decoration {
    animation: none;
  }

  .login-button,
  .login-input,
  .tab-button {
    transition: none;
  }

  .loading-spinner {
    animation: none;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
  }
}

/* 打印样式 */
@media print {
  .login-bg {
    background: white !important;
  }

  .bg-decoration {
    display: none !important;
  }

  .login-card {
    box-shadow: none !important;
    border: 1px solid #ccc;
  }

  .login-button {
    background: #1868F1 !important;
    color: white !important;
  }
}
