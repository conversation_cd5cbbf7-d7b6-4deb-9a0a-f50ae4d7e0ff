﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="590px" height="372px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="661px" y="300px" width="590px" height="372px" filterUnits="userSpaceOnUse" id="filter13">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="10" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.2 0  " in="shadowComposite" />
    </filter>
    <g id="widget14">
      <path d="M 681 324  A 4 4 0 0 1 685 320 L 1227 320  A 4 4 0 0 1 1231 324 L 1231 648  A 4 4 0 0 1 1227 652 L 685 652  A 4 4 0 0 1 681 648 L 681 324  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" fill-opacity="0.8980392156862745" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -661 -300 )">
    <use xlink:href="#widget14" filter="url(#filter13)" />
    <use xlink:href="#widget14" />
  </g>
</svg>