﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:1912px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2207_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:1505px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2207 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:1505px;
  display:flex;
}
#u2207 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2207_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2209_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:884px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:10px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  -webkit-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
}
#u2209 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:200px;
  height:884px;
  display:flex;
}
#u2209 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2209_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2210_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u2210 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u2210 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2210_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2211_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:70px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  -webkit-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
}
#u2211 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:70px;
  display:flex;
}
#u2211 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2211_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2212 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2213_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u2213 {
  border-width:0px;
  position:absolute;
  left:1840px;
  top:20px;
  width:30px;
  height:30px;
  display:flex;
}
#u2213 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2213_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2214_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:4px;
}
#u2214 {
  border-width:0px;
  position:absolute;
  left:1875px;
  top:33px;
  width:7px;
  height:4px;
  display:flex;
  color:#555555;
}
#u2214 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2214_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2215 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2216_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2216 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:26px;
  width:73px;
  height:19px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2216 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2216_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2217_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u2217 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:20px;
  width:30px;
  height:30px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u2217 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2217_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2218 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2219_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:153px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:0px 0px 10px rgba(85, 85, 85, 0.34901960784313724);
  -webkit-box-shadow:0px 0px 10px rgba(85, 85, 85, 0.34901960784313724);
  box-shadow:0px 0px 10px rgba(85, 85, 85, 0.34901960784313724);
}
#u2219 {
  border-width:0px;
  position:absolute;
  left:1762px;
  top:59px;
  width:150px;
  height:153px;
  display:flex;
}
#u2219 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2219_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2220_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:center;
}
#u2220 {
  border-width:0px;
  position:absolute;
  left:1816px;
  top:79px;
  width:42px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:center;
}
#u2220 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2220_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2221_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u2221 {
  border-width:0px;
  position:absolute;
  left:1809px;
  top:134px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u2221 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2221_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2222_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u2222 {
  border-width:0px;
  position:absolute;
  left:1809px;
  top:178px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u2222 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2222_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2223_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:2px;
}
#u2223 {
  border-width:0px;
  position:absolute;
  left:1772px;
  top:113px;
  width:130px;
  height:1px;
  display:flex;
}
#u2223 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2223_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2224_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u2224 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:122px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u2224 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2224_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2225_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u2225 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:174px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u2225 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2225_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2226_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u2226 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:226px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u2226 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2226_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2227_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2227 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:88px;
  width:70px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2227 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2227_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2228_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2228 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:140px;
  width:84px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2228 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2228_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2229_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2229 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:192px;
  width:129px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2229 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2229_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2230_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2230 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:244px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u2230 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2230_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2231_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2231 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:241px;
  width:20px;
  height:20px;
  display:flex;
}
#u2231 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2231_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2232_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2232 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:85px;
  width:20px;
  height:20px;
  display:flex;
}
#u2232 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2232_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2233_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2233 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:137px;
  width:20px;
  height:20px;
  display:flex;
}
#u2233 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2233_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2234_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2234 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:189px;
  width:20px;
  height:20px;
  display:flex;
}
#u2234 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2234_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2235_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1692px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2235 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:80px;
  width:1692px;
  height:60px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2235 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2235_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2236_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:3px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2236 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:137px;
  width:60px;
  height:3px;
  display:flex;
}
#u2236 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2236_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2237_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u2237 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:102px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u2237 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2237_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2238_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u2238 {
  border-width:0px;
  position:absolute;
  left:394px;
  top:102px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u2238 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2238_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2239_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u2239 {
  border-width:0px;
  position:absolute;
  left:518px;
  top:102px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u2239 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2239_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2240_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
  text-align:center;
}
#u2240 {
  border-width:0px;
  position:absolute;
  left:642px;
  top:102px;
  width:80px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
  text-align:center;
}
#u2240 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2240_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2241_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:1345px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2241 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:150px;
  width:1200px;
  height:1345px;
  display:flex;
  opacity:0.9;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2241 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2241_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2242_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2242 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:165px;
  width:144px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2242 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2242_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2243_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:5px;
  height:23px;
}
#u2243 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:163px;
  width:2px;
  height:20px;
  display:flex;
}
#u2243 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2243_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2244_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:580px;
  height:310px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2244 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:210px;
  width:580px;
  height:310px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2244 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2244_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2245_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2245 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:220px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2245 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2245_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2246_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2246 {
  border-width:0px;
  position:absolute;
  left:443px;
  top:499px;
  width:10px;
  height:10px;
  display:flex;
}
#u2246 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2246_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2247_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2247 {
  border-width:0px;
  position:absolute;
  left:458px;
  top:498px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2247 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2247_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2248_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u2248 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:499px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u2248 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2248_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2249_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2249 {
  border-width:0px;
  position:absolute;
  left:527px;
  top:498px;
  width:60px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2249 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2249_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2250 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2251_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2251 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:454px;
  width:500px;
  height:1px;
  display:flex;
}
#u2251 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2251_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2252_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2252 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:388px;
  width:500px;
  height:1px;
  display:flex;
}
#u2252 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2252_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2253_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2253 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:355px;
  width:500px;
  height:1px;
  display:flex;
}
#u2253 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2253_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2254_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2254 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:322px;
  width:500px;
  height:1px;
  display:flex;
}
#u2254 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2254_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2255_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2255 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:288px;
  width:500px;
  height:1px;
  display:flex;
}
#u2255 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2255_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2256_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2256 {
  border-width:0px;
  position:absolute;
  left:248px;
  top:445px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2256 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2256_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2257_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2257 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:346px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2257 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2257_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2258_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2258 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:279px;
  width:20px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2258 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2258_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2259_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2259 {
  border-width:0px;
  position:absolute;
  left:290px;
  top:455px;
  width:1px;
  height:5px;
  display:flex;
}
#u2259 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2259_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2260_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2260 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:465px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2260 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2260_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2261_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2261 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:379px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2261 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2261_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2262_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2262 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:313px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2262 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2262_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2263_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2263 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:421px;
  width:500px;
  height:1px;
  display:flex;
}
#u2263 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2263_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2264_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2264 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:412px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2264 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2264_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2265_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2265 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:455px;
  width:1px;
  height:5px;
  display:flex;
}
#u2265 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2265_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2266_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2266 {
  border-width:0px;
  position:absolute;
  left:338px;
  top:465px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2266 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2266_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2267_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2267 {
  border-width:0px;
  position:absolute;
  left:436px;
  top:455px;
  width:1px;
  height:5px;
  display:flex;
}
#u2267 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2267_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2268_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2268 {
  border-width:0px;
  position:absolute;
  left:411px;
  top:465px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2268 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2268_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2269_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2269 {
  border-width:0px;
  position:absolute;
  left:510px;
  top:455px;
  width:1px;
  height:5px;
  display:flex;
}
#u2269 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2269_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2270_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2270 {
  border-width:0px;
  position:absolute;
  left:485px;
  top:465px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2270 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2270_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2271_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2271 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:249px;
  width:45px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2271 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2271_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2272_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2272 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:445px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2272 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2272_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2273_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2273 {
  border-width:0px;
  position:absolute;
  left:583px;
  top:455px;
  width:1px;
  height:5px;
  display:flex;
}
#u2273 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2273_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2274_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2274 {
  border-width:0px;
  position:absolute;
  left:558px;
  top:465px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2274 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2274_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2275_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2275 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:346px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2275 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2275_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2276_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2276 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:279px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2276 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2276_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2277_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2277 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:379px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2277 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2277_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2278_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2278 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:313px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2278 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2278_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2279_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2279 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:412px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2279 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2279_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2280_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2280 {
  border-width:0px;
  position:absolute;
  left:735px;
  top:249px;
  width:60px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2280 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2280_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2281_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2281 {
  border-width:0px;
  position:absolute;
  left:656px;
  top:455px;
  width:1px;
  height:5px;
  display:flex;
}
#u2281 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2281_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2282_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2282 {
  border-width:0px;
  position:absolute;
  left:631px;
  top:465px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2282 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2282_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2283_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2283 {
  border-width:0px;
  position:absolute;
  left:729px;
  top:455px;
  width:1px;
  height:5px;
  display:flex;
}
#u2283 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2283_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2284_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2284 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:465px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2284 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2284_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2285_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:126px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2285 {
  border-width:0px;
  position:absolute;
  left:709px;
  top:328px;
  width:20px;
  height:126px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2285 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2285_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2286_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:141px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2286 {
  border-width:0px;
  position:absolute;
  left:730px;
  top:313px;
  width:20px;
  height:141px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2286 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2286_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2287_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:107px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2287 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:347px;
  width:20px;
  height:107px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2287 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2287_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2288_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2288 {
  border-width:0px;
  position:absolute;
  left:291px;
  top:334px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2288 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2288_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2289_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:107px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2289 {
  border-width:0px;
  position:absolute;
  left:636px;
  top:347px;
  width:20px;
  height:107px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2289 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2289_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2290_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:113px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2290 {
  border-width:0px;
  position:absolute;
  left:657px;
  top:341px;
  width:20px;
  height:113px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2290 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2290_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2291_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:118px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2291 {
  border-width:0px;
  position:absolute;
  left:563px;
  top:336px;
  width:20px;
  height:118px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2291 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2291_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2292_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:95px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2292 {
  border-width:0px;
  position:absolute;
  left:584px;
  top:359px;
  width:20px;
  height:95px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2292 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2292_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2293_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2293 {
  border-width:0px;
  position:absolute;
  left:490px;
  top:334px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2293 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2293_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2294_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:129px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2294 {
  border-width:0px;
  position:absolute;
  left:511px;
  top:325px;
  width:20px;
  height:129px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2294 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2294_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2295_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:126px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2295 {
  border-width:0px;
  position:absolute;
  left:416px;
  top:328px;
  width:20px;
  height:126px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2295 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2295_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2296_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:113px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2296 {
  border-width:0px;
  position:absolute;
  left:437px;
  top:341px;
  width:20px;
  height:113px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2296 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2296_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2297_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:145px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2297 {
  border-width:0px;
  position:absolute;
  left:343px;
  top:309px;
  width:20px;
  height:145px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2297 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2297_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2298_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2298 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:334px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2298 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2298_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2299 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2300 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2301 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2302_img {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-10px;
  width:153px;
  height:79px;
}
#u2302 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:328px;
  width:133px;
  height:59px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2302 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2302_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2303_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2303 {
  border-width:0px;
  position:absolute;
  left:472px;
  top:355px;
  width:47px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2303 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2303_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2304_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u2304 {
  border-width:0px;
  position:absolute;
  left:472px;
  top:338px;
  width:51px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u2304 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2304_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2305_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u2305 {
  border-width:0px;
  position:absolute;
  left:477px;
  top:363px;
  width:6px;
  height:6px;
  display:flex;
}
#u2305 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2305_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2306_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  text-align:right;
  line-height:12px;
}
#u2306 {
  border-width:0px;
  position:absolute;
  left:488px;
  top:360px;
  width:26px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  text-align:right;
  line-height:12px;
}
#u2306 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2306_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2307_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2307 {
  border-width:0px;
  position:absolute;
  left:524px;
  top:355px;
  width:61px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2307 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2307_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2308_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u2308 {
  border-width:0px;
  position:absolute;
  left:529px;
  top:363px;
  width:6px;
  height:6px;
  display:flex;
}
#u2308 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2308_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2309_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#2FC25B;
  text-align:right;
  line-height:12px;
}
#u2309 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:360px;
  width:40px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#2FC25B;
  text-align:right;
  line-height:12px;
}
#u2309 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2309_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2310 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2311_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:40px;
}
#u2311 {
  border-width:0px;
  position:absolute;
  left:1212px;
  top:160px;
  width:198px;
  height:40px;
  display:flex;
}
#u2311 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2311_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2312_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u2312 {
  border-width:0px;
  position:absolute;
  left:1227px;
  top:173px;
  width:112px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u2312 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2312_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2313_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u2313 {
  border-width:0px;
  position:absolute;
  left:1349px;
  top:165px;
  width:30px;
  height:30px;
  display:flex;
}
#u2313 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2313_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2314_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:20px;
}
#u2314 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:170px;
  width:18px;
  height:20px;
  display:flex;
}
#u2314 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2314_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2315_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:10px;
}
#u2315 {
  border-width:0px;
  position:absolute;
  left:1389px;
  top:175px;
  width:6px;
  height:10px;
  display:flex;
}
#u2315 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2315_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2316_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:580px;
  height:310px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2316 {
  border-width:0px;
  position:absolute;
  left:815px;
  top:210px;
  width:580px;
  height:310px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2316 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2316_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2317_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2317 {
  border-width:0px;
  position:absolute;
  left:825px;
  top:220px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2317 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2317_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2318_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2318 {
  border-width:0px;
  position:absolute;
  left:1033px;
  top:499px;
  width:10px;
  height:10px;
  display:flex;
}
#u2318 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2318_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2319_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2319 {
  border-width:0px;
  position:absolute;
  left:1048px;
  top:498px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2319 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2319_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2320_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u2320 {
  border-width:0px;
  position:absolute;
  left:1102px;
  top:499px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u2320 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2320_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2321_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2321 {
  border-width:0px;
  position:absolute;
  left:1117px;
  top:498px;
  width:60px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2321 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2321_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2322 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2323_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2323 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:454px;
  width:500px;
  height:1px;
  display:flex;
}
#u2323 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2323_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2324_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2324 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:388px;
  width:500px;
  height:1px;
  display:flex;
}
#u2324 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2324_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2325_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2325 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:355px;
  width:500px;
  height:1px;
  display:flex;
}
#u2325 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2325_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2326_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2326 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:322px;
  width:500px;
  height:1px;
  display:flex;
}
#u2326 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2326_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2327_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2327 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:288px;
  width:500px;
  height:1px;
  display:flex;
}
#u2327 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2327_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2328_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2328 {
  border-width:0px;
  position:absolute;
  left:838px;
  top:445px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2328 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2328_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2329_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2329 {
  border-width:0px;
  position:absolute;
  left:831px;
  top:346px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2329 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2329_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2330_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2330 {
  border-width:0px;
  position:absolute;
  left:825px;
  top:279px;
  width:20px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2330 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2330_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2331_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2331 {
  border-width:0px;
  position:absolute;
  left:880px;
  top:455px;
  width:1px;
  height:5px;
  display:flex;
}
#u2331 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2331_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2332_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2332 {
  border-width:0px;
  position:absolute;
  left:855px;
  top:465px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2332 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2332_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2333_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2333 {
  border-width:0px;
  position:absolute;
  left:831px;
  top:379px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2333 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2333_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2334_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2334 {
  border-width:0px;
  position:absolute;
  left:831px;
  top:313px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2334 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2334_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2335_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2335 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:421px;
  width:500px;
  height:1px;
  display:flex;
}
#u2335 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2335_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2336_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2336 {
  border-width:0px;
  position:absolute;
  left:831px;
  top:412px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2336 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2336_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2337_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2337 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:455px;
  width:1px;
  height:5px;
  display:flex;
}
#u2337 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2337_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2338_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2338 {
  border-width:0px;
  position:absolute;
  left:928px;
  top:465px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2338 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2338_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2339_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2339 {
  border-width:0px;
  position:absolute;
  left:1026px;
  top:455px;
  width:1px;
  height:5px;
  display:flex;
}
#u2339 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2339_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2340_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2340 {
  border-width:0px;
  position:absolute;
  left:1001px;
  top:465px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2340 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2340_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2341_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2341 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:455px;
  width:1px;
  height:5px;
  display:flex;
}
#u2341 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2341_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2342_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2342 {
  border-width:0px;
  position:absolute;
  left:1075px;
  top:465px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2342 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2342_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2343_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2343 {
  border-width:0px;
  position:absolute;
  left:825px;
  top:249px;
  width:45px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2343 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2343_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2344_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2344 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:445px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2344 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2344_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2345_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2345 {
  border-width:0px;
  position:absolute;
  left:1173px;
  top:455px;
  width:1px;
  height:5px;
  display:flex;
}
#u2345 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2345_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2346_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2346 {
  border-width:0px;
  position:absolute;
  left:1148px;
  top:465px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2346 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2346_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2347_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2347 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:346px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2347 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2347_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2348_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2348 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:279px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2348 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2348_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2349_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2349 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:379px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2349 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2349_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2350_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2350 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:313px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2350 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2350_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2351_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2351 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:412px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2351 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2351_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2352_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2352 {
  border-width:0px;
  position:absolute;
  left:1325px;
  top:249px;
  width:60px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2352 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2352_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2353_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2353 {
  border-width:0px;
  position:absolute;
  left:1246px;
  top:455px;
  width:1px;
  height:5px;
  display:flex;
}
#u2353 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2353_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2354_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2354 {
  border-width:0px;
  position:absolute;
  left:1221px;
  top:465px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2354 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2354_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2355_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2355 {
  border-width:0px;
  position:absolute;
  left:1319px;
  top:455px;
  width:1px;
  height:5px;
  display:flex;
}
#u2355 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2355_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2356_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2356 {
  border-width:0px;
  position:absolute;
  left:1294px;
  top:465px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2356 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2356_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2357_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:126px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2357 {
  border-width:0px;
  position:absolute;
  left:1299px;
  top:328px;
  width:20px;
  height:126px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2357 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2357_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2358_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:141px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2358 {
  border-width:0px;
  position:absolute;
  left:1320px;
  top:313px;
  width:20px;
  height:141px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2358 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2358_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2359_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:107px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2359 {
  border-width:0px;
  position:absolute;
  left:860px;
  top:347px;
  width:20px;
  height:107px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2359 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2359_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2360_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2360 {
  border-width:0px;
  position:absolute;
  left:881px;
  top:334px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2360 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2360_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2361_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:107px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2361 {
  border-width:0px;
  position:absolute;
  left:1226px;
  top:347px;
  width:20px;
  height:107px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2361 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2361_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2362_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:113px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2362 {
  border-width:0px;
  position:absolute;
  left:1247px;
  top:341px;
  width:20px;
  height:113px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2362 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2362_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2363_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:118px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2363 {
  border-width:0px;
  position:absolute;
  left:1153px;
  top:336px;
  width:20px;
  height:118px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2363 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2363_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2364_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:95px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2364 {
  border-width:0px;
  position:absolute;
  left:1174px;
  top:359px;
  width:20px;
  height:95px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2364 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2364_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2365_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2365 {
  border-width:0px;
  position:absolute;
  left:1080px;
  top:334px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2365 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2365_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2366_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:129px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2366 {
  border-width:0px;
  position:absolute;
  left:1101px;
  top:325px;
  width:20px;
  height:129px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2366 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2366_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2367_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:126px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2367 {
  border-width:0px;
  position:absolute;
  left:1006px;
  top:328px;
  width:20px;
  height:126px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2367 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2367_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2368_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:113px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2368 {
  border-width:0px;
  position:absolute;
  left:1027px;
  top:341px;
  width:20px;
  height:113px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2368 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2368_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2369_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:145px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2369 {
  border-width:0px;
  position:absolute;
  left:933px;
  top:309px;
  width:20px;
  height:145px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2369 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2369_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2370_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2370 {
  border-width:0px;
  position:absolute;
  left:954px;
  top:334px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2370 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2370_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2371_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:580px;
  height:310px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2371 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:530px;
  width:580px;
  height:310px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2371 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2371_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2372_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2372 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:540px;
  width:98px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2372 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2372_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2373_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2373 {
  border-width:0px;
  position:absolute;
  left:443px;
  top:819px;
  width:10px;
  height:10px;
  display:flex;
}
#u2373 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2373_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2374_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2374 {
  border-width:0px;
  position:absolute;
  left:458px;
  top:818px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2374 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2374_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2375_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u2375 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:819px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u2375 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2375_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2376_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2376 {
  border-width:0px;
  position:absolute;
  left:527px;
  top:818px;
  width:60px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2376 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2376_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2377 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2378_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2378 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:774px;
  width:500px;
  height:1px;
  display:flex;
}
#u2378 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2378_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2379_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2379 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:708px;
  width:500px;
  height:1px;
  display:flex;
}
#u2379 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2379_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2380_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2380 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:675px;
  width:500px;
  height:1px;
  display:flex;
}
#u2380 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2380_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2381_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2381 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:642px;
  width:500px;
  height:1px;
  display:flex;
}
#u2381 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2381_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2382_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2382 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:608px;
  width:500px;
  height:1px;
  display:flex;
}
#u2382 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2382_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2383_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2383 {
  border-width:0px;
  position:absolute;
  left:248px;
  top:765px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2383 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2383_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2384_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2384 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:666px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2384 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2384_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2385_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2385 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:599px;
  width:20px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2385 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2385_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2386_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2386 {
  border-width:0px;
  position:absolute;
  left:290px;
  top:775px;
  width:1px;
  height:5px;
  display:flex;
}
#u2386 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2386_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2387_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2387 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:785px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2387 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2387_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2388_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2388 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:699px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2388 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2388_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2389_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2389 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:633px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2389 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2389_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2390_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2390 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:741px;
  width:500px;
  height:1px;
  display:flex;
}
#u2390 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2390_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2391_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2391 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:732px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2391 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2391_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2392_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2392 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:775px;
  width:1px;
  height:5px;
  display:flex;
}
#u2392 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2392_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2393_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2393 {
  border-width:0px;
  position:absolute;
  left:338px;
  top:785px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2393 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2393_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2394_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2394 {
  border-width:0px;
  position:absolute;
  left:436px;
  top:775px;
  width:1px;
  height:5px;
  display:flex;
}
#u2394 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2394_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2395_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2395 {
  border-width:0px;
  position:absolute;
  left:411px;
  top:785px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2395 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2395_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2396_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2396 {
  border-width:0px;
  position:absolute;
  left:510px;
  top:775px;
  width:1px;
  height:5px;
  display:flex;
}
#u2396 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2396_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2397_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2397 {
  border-width:0px;
  position:absolute;
  left:485px;
  top:785px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2397 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2397_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2398_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2398 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:569px;
  width:45px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2398 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2398_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2399_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2399 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:765px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2399 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2399_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2400_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2400 {
  border-width:0px;
  position:absolute;
  left:583px;
  top:775px;
  width:1px;
  height:5px;
  display:flex;
}
#u2400 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2400_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2401_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2401 {
  border-width:0px;
  position:absolute;
  left:558px;
  top:785px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2401 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2401_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2402_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2402 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:666px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2402 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2402_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2403_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2403 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:599px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2403 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2403_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2404_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2404 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:699px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2404 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2404_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2405_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2405 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:633px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2405 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2405_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2406_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2406 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:732px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2406 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2406_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2407_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2407 {
  border-width:0px;
  position:absolute;
  left:735px;
  top:569px;
  width:60px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2407 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2407_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2408_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2408 {
  border-width:0px;
  position:absolute;
  left:656px;
  top:775px;
  width:1px;
  height:5px;
  display:flex;
}
#u2408 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2408_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2409_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2409 {
  border-width:0px;
  position:absolute;
  left:631px;
  top:785px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2409 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2409_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2410_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2410 {
  border-width:0px;
  position:absolute;
  left:729px;
  top:775px;
  width:1px;
  height:5px;
  display:flex;
}
#u2410 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2410_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2411_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2411 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:785px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2411 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2411_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2412_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:126px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2412 {
  border-width:0px;
  position:absolute;
  left:709px;
  top:648px;
  width:20px;
  height:126px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2412 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2412_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2413_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:141px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2413 {
  border-width:0px;
  position:absolute;
  left:730px;
  top:633px;
  width:20px;
  height:141px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2413 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2413_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2414_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:107px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2414 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:667px;
  width:20px;
  height:107px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2414 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2414_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2415_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2415 {
  border-width:0px;
  position:absolute;
  left:291px;
  top:654px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2415 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2415_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2416_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:107px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2416 {
  border-width:0px;
  position:absolute;
  left:636px;
  top:667px;
  width:20px;
  height:107px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2416 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2416_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2417_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:113px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2417 {
  border-width:0px;
  position:absolute;
  left:657px;
  top:661px;
  width:20px;
  height:113px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2417 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2417_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2418_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:118px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2418 {
  border-width:0px;
  position:absolute;
  left:563px;
  top:656px;
  width:20px;
  height:118px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2418 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2418_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2419_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:95px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2419 {
  border-width:0px;
  position:absolute;
  left:584px;
  top:679px;
  width:20px;
  height:95px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2419 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2419_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2420_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2420 {
  border-width:0px;
  position:absolute;
  left:490px;
  top:654px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2420 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2420_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2421_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:129px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2421 {
  border-width:0px;
  position:absolute;
  left:511px;
  top:645px;
  width:20px;
  height:129px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2421 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2421_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2422_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:126px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2422 {
  border-width:0px;
  position:absolute;
  left:416px;
  top:648px;
  width:20px;
  height:126px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2422 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2422_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2423_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:113px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2423 {
  border-width:0px;
  position:absolute;
  left:437px;
  top:661px;
  width:20px;
  height:113px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2423 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2423_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2424_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:145px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2424 {
  border-width:0px;
  position:absolute;
  left:343px;
  top:629px;
  width:20px;
  height:145px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2424 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2424_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2425_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2425 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:654px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2425 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2425_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2426_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:580px;
  height:310px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2426 {
  border-width:0px;
  position:absolute;
  left:815px;
  top:530px;
  width:580px;
  height:310px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2426 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2426_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2427_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2427 {
  border-width:0px;
  position:absolute;
  left:825px;
  top:540px;
  width:84px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2427 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2427_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2428_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2428 {
  border-width:0px;
  position:absolute;
  left:1033px;
  top:819px;
  width:10px;
  height:10px;
  display:flex;
}
#u2428 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2428_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2429_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2429 {
  border-width:0px;
  position:absolute;
  left:1048px;
  top:818px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2429 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2429_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2430_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u2430 {
  border-width:0px;
  position:absolute;
  left:1102px;
  top:819px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u2430 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2430_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2431_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2431 {
  border-width:0px;
  position:absolute;
  left:1117px;
  top:818px;
  width:60px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2431 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2431_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2432 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2433_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2433 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:774px;
  width:500px;
  height:1px;
  display:flex;
}
#u2433 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2433_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2434_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2434 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:708px;
  width:500px;
  height:1px;
  display:flex;
}
#u2434 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2434_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2435_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2435 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:675px;
  width:500px;
  height:1px;
  display:flex;
}
#u2435 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2435_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2436_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2436 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:642px;
  width:500px;
  height:1px;
  display:flex;
}
#u2436 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2436_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2437_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2437 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:608px;
  width:500px;
  height:1px;
  display:flex;
}
#u2437 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2437_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2438_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2438 {
  border-width:0px;
  position:absolute;
  left:838px;
  top:765px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2438 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2438_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2439_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2439 {
  border-width:0px;
  position:absolute;
  left:831px;
  top:666px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2439 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2439_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2440_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2440 {
  border-width:0px;
  position:absolute;
  left:825px;
  top:599px;
  width:20px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2440 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2440_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2441_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2441 {
  border-width:0px;
  position:absolute;
  left:880px;
  top:775px;
  width:1px;
  height:5px;
  display:flex;
}
#u2441 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2441_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2442_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2442 {
  border-width:0px;
  position:absolute;
  left:855px;
  top:785px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2442 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2442_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2443_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2443 {
  border-width:0px;
  position:absolute;
  left:831px;
  top:699px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2443 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2443_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2444_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2444 {
  border-width:0px;
  position:absolute;
  left:831px;
  top:633px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2444 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2444_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2445_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2445 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:741px;
  width:500px;
  height:1px;
  display:flex;
}
#u2445 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2445_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2446_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2446 {
  border-width:0px;
  position:absolute;
  left:831px;
  top:732px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2446 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2446_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2447_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2447 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:775px;
  width:1px;
  height:5px;
  display:flex;
}
#u2447 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2447_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2448_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2448 {
  border-width:0px;
  position:absolute;
  left:928px;
  top:785px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2448 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2448_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2449_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2449 {
  border-width:0px;
  position:absolute;
  left:1026px;
  top:775px;
  width:1px;
  height:5px;
  display:flex;
}
#u2449 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2449_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2450_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2450 {
  border-width:0px;
  position:absolute;
  left:1001px;
  top:785px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2450 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2450_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2451_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2451 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:775px;
  width:1px;
  height:5px;
  display:flex;
}
#u2451 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2451_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2452_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2452 {
  border-width:0px;
  position:absolute;
  left:1075px;
  top:785px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2452 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2452_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2453_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2453 {
  border-width:0px;
  position:absolute;
  left:825px;
  top:569px;
  width:45px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2453 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2453_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2454_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2454 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:765px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2454 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2454_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2455_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2455 {
  border-width:0px;
  position:absolute;
  left:1173px;
  top:775px;
  width:1px;
  height:5px;
  display:flex;
}
#u2455 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2455_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2456_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2456 {
  border-width:0px;
  position:absolute;
  left:1148px;
  top:785px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2456 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2456_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2457_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2457 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:666px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2457 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2457_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2458_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2458 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:599px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2458 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2458_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2459_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2459 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:699px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2459 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2459_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2460_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2460 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:633px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2460 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2460_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2461_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2461 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:732px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2461 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2461_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2462_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2462 {
  border-width:0px;
  position:absolute;
  left:1325px;
  top:569px;
  width:60px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2462 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2462_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2463_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2463 {
  border-width:0px;
  position:absolute;
  left:1246px;
  top:775px;
  width:1px;
  height:5px;
  display:flex;
}
#u2463 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2463_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2464_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2464 {
  border-width:0px;
  position:absolute;
  left:1221px;
  top:785px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2464 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2464_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2465_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2465 {
  border-width:0px;
  position:absolute;
  left:1319px;
  top:775px;
  width:1px;
  height:5px;
  display:flex;
}
#u2465 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2465_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2466_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2466 {
  border-width:0px;
  position:absolute;
  left:1294px;
  top:785px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2466 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2466_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2467_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:126px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2467 {
  border-width:0px;
  position:absolute;
  left:1299px;
  top:648px;
  width:20px;
  height:126px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2467 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2467_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2468_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:141px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2468 {
  border-width:0px;
  position:absolute;
  left:1320px;
  top:633px;
  width:20px;
  height:141px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2468 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2468_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2469_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:107px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2469 {
  border-width:0px;
  position:absolute;
  left:860px;
  top:667px;
  width:20px;
  height:107px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2469 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2469_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2470_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2470 {
  border-width:0px;
  position:absolute;
  left:881px;
  top:654px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2470 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2470_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2471_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:107px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2471 {
  border-width:0px;
  position:absolute;
  left:1226px;
  top:667px;
  width:20px;
  height:107px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2471 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2471_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2472_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:113px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2472 {
  border-width:0px;
  position:absolute;
  left:1247px;
  top:661px;
  width:20px;
  height:113px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2472 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2472_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2473_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:118px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2473 {
  border-width:0px;
  position:absolute;
  left:1153px;
  top:656px;
  width:20px;
  height:118px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2473 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2473_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2474_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:95px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2474 {
  border-width:0px;
  position:absolute;
  left:1174px;
  top:679px;
  width:20px;
  height:95px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2474 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2474_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2475_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2475 {
  border-width:0px;
  position:absolute;
  left:1080px;
  top:654px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2475 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2475_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2476_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:129px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2476 {
  border-width:0px;
  position:absolute;
  left:1101px;
  top:645px;
  width:20px;
  height:129px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2476 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2476_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2477_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:126px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2477 {
  border-width:0px;
  position:absolute;
  left:1006px;
  top:648px;
  width:20px;
  height:126px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2477 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2477_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2478_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:113px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2478 {
  border-width:0px;
  position:absolute;
  left:1027px;
  top:661px;
  width:20px;
  height:113px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2478 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2478_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2479_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:145px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2479 {
  border-width:0px;
  position:absolute;
  left:933px;
  top:629px;
  width:20px;
  height:145px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2479 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2479_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2480_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2480 {
  border-width:0px;
  position:absolute;
  left:954px;
  top:654px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2480 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2480_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2481_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:580px;
  height:310px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2481 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:850px;
  width:580px;
  height:310px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2481 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2481_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2482_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2482 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:860px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2482 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2482_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2483_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2483 {
  border-width:0px;
  position:absolute;
  left:443px;
  top:1139px;
  width:10px;
  height:10px;
  display:flex;
}
#u2483 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2483_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2484_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2484 {
  border-width:0px;
  position:absolute;
  left:458px;
  top:1138px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2484 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2484_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2485_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u2485 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:1139px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u2485 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2485_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2486_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2486 {
  border-width:0px;
  position:absolute;
  left:527px;
  top:1138px;
  width:60px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2486 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2486_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2487 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2488_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2488 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:1094px;
  width:500px;
  height:1px;
  display:flex;
}
#u2488 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2488_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2489_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2489 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:1028px;
  width:500px;
  height:1px;
  display:flex;
}
#u2489 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2489_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2490_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2490 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:995px;
  width:500px;
  height:1px;
  display:flex;
}
#u2490 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2490_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2491_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2491 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:962px;
  width:500px;
  height:1px;
  display:flex;
}
#u2491 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2491_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2492_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2492 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:928px;
  width:500px;
  height:1px;
  display:flex;
}
#u2492 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2492_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2493_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2493 {
  border-width:0px;
  position:absolute;
  left:248px;
  top:1085px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2493 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2493_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2494_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2494 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:986px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2494 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2494_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2495_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2495 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:919px;
  width:20px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2495 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2495_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2496_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2496 {
  border-width:0px;
  position:absolute;
  left:290px;
  top:1095px;
  width:1px;
  height:5px;
  display:flex;
}
#u2496 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2496_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2497_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2497 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:1105px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2497 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2497_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2498_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2498 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:1019px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2498 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2498_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2499_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2499 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:953px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2499 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2499_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2500_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2500 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:1061px;
  width:500px;
  height:1px;
  display:flex;
}
#u2500 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2500_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2501_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2501 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:1052px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2501 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2501_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2502_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2502 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:1095px;
  width:1px;
  height:5px;
  display:flex;
}
#u2502 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2502_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2503_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2503 {
  border-width:0px;
  position:absolute;
  left:338px;
  top:1105px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2503 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2503_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2504_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2504 {
  border-width:0px;
  position:absolute;
  left:436px;
  top:1095px;
  width:1px;
  height:5px;
  display:flex;
}
#u2504 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2504_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2505_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2505 {
  border-width:0px;
  position:absolute;
  left:411px;
  top:1105px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2505 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2505_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2506_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2506 {
  border-width:0px;
  position:absolute;
  left:510px;
  top:1095px;
  width:1px;
  height:5px;
  display:flex;
}
#u2506 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2506_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2507_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2507 {
  border-width:0px;
  position:absolute;
  left:485px;
  top:1105px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2507 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2507_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2508_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2508 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:889px;
  width:45px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2508 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2508_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2509_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2509 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:1085px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2509 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2509_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2510_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2510 {
  border-width:0px;
  position:absolute;
  left:583px;
  top:1095px;
  width:1px;
  height:5px;
  display:flex;
}
#u2510 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2510_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2511_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2511 {
  border-width:0px;
  position:absolute;
  left:558px;
  top:1105px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2511 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2511_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2512_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2512 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:986px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2512 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2512_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2513_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2513 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:919px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2513 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2513_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2514_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2514 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:1019px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2514 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2514_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2515_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2515 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:953px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2515 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2515_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2516_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2516 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:1052px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2516 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2516_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2517_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2517 {
  border-width:0px;
  position:absolute;
  left:735px;
  top:889px;
  width:60px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2517 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2517_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2518_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2518 {
  border-width:0px;
  position:absolute;
  left:656px;
  top:1095px;
  width:1px;
  height:5px;
  display:flex;
}
#u2518 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2518_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2519_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2519 {
  border-width:0px;
  position:absolute;
  left:631px;
  top:1105px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2519 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2519_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2520_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2520 {
  border-width:0px;
  position:absolute;
  left:729px;
  top:1095px;
  width:1px;
  height:5px;
  display:flex;
}
#u2520 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2520_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2521_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2521 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:1105px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2521 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2521_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2522_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:126px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2522 {
  border-width:0px;
  position:absolute;
  left:709px;
  top:968px;
  width:20px;
  height:126px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2522 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2522_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2523_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:141px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2523 {
  border-width:0px;
  position:absolute;
  left:730px;
  top:953px;
  width:20px;
  height:141px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2523 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2523_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2524_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:107px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2524 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:987px;
  width:20px;
  height:107px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2524 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2524_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2525_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2525 {
  border-width:0px;
  position:absolute;
  left:291px;
  top:974px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2525 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2525_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2526_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:107px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2526 {
  border-width:0px;
  position:absolute;
  left:636px;
  top:987px;
  width:20px;
  height:107px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2526 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2526_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2527_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:113px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2527 {
  border-width:0px;
  position:absolute;
  left:657px;
  top:981px;
  width:20px;
  height:113px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2527 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2527_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2528_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:118px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2528 {
  border-width:0px;
  position:absolute;
  left:563px;
  top:976px;
  width:20px;
  height:118px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2528 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2528_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2529_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:95px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2529 {
  border-width:0px;
  position:absolute;
  left:584px;
  top:999px;
  width:20px;
  height:95px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2529 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2529_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2530_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2530 {
  border-width:0px;
  position:absolute;
  left:490px;
  top:974px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2530 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2530_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2531_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:129px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2531 {
  border-width:0px;
  position:absolute;
  left:511px;
  top:965px;
  width:20px;
  height:129px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2531 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2531_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2532_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:126px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2532 {
  border-width:0px;
  position:absolute;
  left:416px;
  top:968px;
  width:20px;
  height:126px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2532 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2532_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2533_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:113px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2533 {
  border-width:0px;
  position:absolute;
  left:437px;
  top:981px;
  width:20px;
  height:113px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2533 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2533_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2534_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:145px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2534 {
  border-width:0px;
  position:absolute;
  left:343px;
  top:949px;
  width:20px;
  height:145px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2534 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2534_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2535_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2535 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:974px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2535 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2535_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2536_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:580px;
  height:310px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2536 {
  border-width:0px;
  position:absolute;
  left:815px;
  top:850px;
  width:580px;
  height:310px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2536 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2536_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2537_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2537 {
  border-width:0px;
  position:absolute;
  left:825px;
  top:860px;
  width:84px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2537 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2537_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2538_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2538 {
  border-width:0px;
  position:absolute;
  left:1033px;
  top:1139px;
  width:10px;
  height:10px;
  display:flex;
}
#u2538 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2538_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2539_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2539 {
  border-width:0px;
  position:absolute;
  left:1048px;
  top:1138px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2539 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2539_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2540_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u2540 {
  border-width:0px;
  position:absolute;
  left:1102px;
  top:1139px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u2540 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2540_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2541_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2541 {
  border-width:0px;
  position:absolute;
  left:1117px;
  top:1138px;
  width:60px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2541 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2541_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2542 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2543_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2543 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:1094px;
  width:500px;
  height:1px;
  display:flex;
}
#u2543 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2543_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2544_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2544 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:1028px;
  width:500px;
  height:1px;
  display:flex;
}
#u2544 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2544_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2545_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2545 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:995px;
  width:500px;
  height:1px;
  display:flex;
}
#u2545 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2545_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2546_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2546 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:962px;
  width:500px;
  height:1px;
  display:flex;
}
#u2546 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2546_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2547_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2547 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:928px;
  width:500px;
  height:1px;
  display:flex;
}
#u2547 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2547_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2548_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2548 {
  border-width:0px;
  position:absolute;
  left:838px;
  top:1085px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2548 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2548_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2549_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2549 {
  border-width:0px;
  position:absolute;
  left:831px;
  top:986px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2549 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2549_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2550_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2550 {
  border-width:0px;
  position:absolute;
  left:825px;
  top:919px;
  width:20px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2550 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2550_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2551_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2551 {
  border-width:0px;
  position:absolute;
  left:880px;
  top:1095px;
  width:1px;
  height:5px;
  display:flex;
}
#u2551 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2551_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2552_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2552 {
  border-width:0px;
  position:absolute;
  left:855px;
  top:1105px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2552 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2552_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2553_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2553 {
  border-width:0px;
  position:absolute;
  left:831px;
  top:1019px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2553 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2553_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2554_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2554 {
  border-width:0px;
  position:absolute;
  left:831px;
  top:953px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2554 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2554_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2555_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2555 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:1061px;
  width:500px;
  height:1px;
  display:flex;
}
#u2555 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2555_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2556_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2556 {
  border-width:0px;
  position:absolute;
  left:831px;
  top:1052px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2556 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2556_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2557_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2557 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:1095px;
  width:1px;
  height:5px;
  display:flex;
}
#u2557 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2557_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2558_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2558 {
  border-width:0px;
  position:absolute;
  left:928px;
  top:1105px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2558 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2558_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2559_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2559 {
  border-width:0px;
  position:absolute;
  left:1026px;
  top:1095px;
  width:1px;
  height:5px;
  display:flex;
}
#u2559 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2559_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2560_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2560 {
  border-width:0px;
  position:absolute;
  left:1001px;
  top:1105px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2560 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2560_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2561_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2561 {
  border-width:0px;
  position:absolute;
  left:1100px;
  top:1095px;
  width:1px;
  height:5px;
  display:flex;
}
#u2561 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2561_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2562_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2562 {
  border-width:0px;
  position:absolute;
  left:1075px;
  top:1105px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2562 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2562_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2563_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2563 {
  border-width:0px;
  position:absolute;
  left:825px;
  top:889px;
  width:45px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2563 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2563_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2564_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2564 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:1085px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2564 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2564_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2565_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2565 {
  border-width:0px;
  position:absolute;
  left:1173px;
  top:1095px;
  width:1px;
  height:5px;
  display:flex;
}
#u2565 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2565_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2566_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2566 {
  border-width:0px;
  position:absolute;
  left:1148px;
  top:1105px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2566 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2566_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2567_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2567 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:986px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2567 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2567_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2568_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2568 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:919px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2568 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2568_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2569_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2569 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:1019px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2569 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2569_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2570_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2570 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:953px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2570 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2570_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2571_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2571 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:1052px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2571 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2571_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2572_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2572 {
  border-width:0px;
  position:absolute;
  left:1325px;
  top:889px;
  width:60px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2572 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2572_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2573_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2573 {
  border-width:0px;
  position:absolute;
  left:1246px;
  top:1095px;
  width:1px;
  height:5px;
  display:flex;
}
#u2573 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2573_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2574_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2574 {
  border-width:0px;
  position:absolute;
  left:1221px;
  top:1105px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2574 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2574_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2575_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2575 {
  border-width:0px;
  position:absolute;
  left:1319px;
  top:1095px;
  width:1px;
  height:5px;
  display:flex;
}
#u2575 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2575_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2576_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2576 {
  border-width:0px;
  position:absolute;
  left:1294px;
  top:1105px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2576 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2576_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2577_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:126px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2577 {
  border-width:0px;
  position:absolute;
  left:1299px;
  top:968px;
  width:20px;
  height:126px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2577 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2577_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2578_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:141px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2578 {
  border-width:0px;
  position:absolute;
  left:1320px;
  top:953px;
  width:20px;
  height:141px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2578 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2578_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2579_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:107px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2579 {
  border-width:0px;
  position:absolute;
  left:860px;
  top:987px;
  width:20px;
  height:107px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2579 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2579_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2580_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2580 {
  border-width:0px;
  position:absolute;
  left:881px;
  top:974px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2580 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2580_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2581_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:107px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2581 {
  border-width:0px;
  position:absolute;
  left:1226px;
  top:987px;
  width:20px;
  height:107px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2581 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2581_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2582_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:113px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2582 {
  border-width:0px;
  position:absolute;
  left:1247px;
  top:981px;
  width:20px;
  height:113px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2582 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2582_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2583_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:118px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2583 {
  border-width:0px;
  position:absolute;
  left:1153px;
  top:976px;
  width:20px;
  height:118px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2583 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2583_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2584_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:95px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2584 {
  border-width:0px;
  position:absolute;
  left:1174px;
  top:999px;
  width:20px;
  height:95px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2584 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2584_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2585_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2585 {
  border-width:0px;
  position:absolute;
  left:1080px;
  top:974px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2585 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2585_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2586_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:129px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2586 {
  border-width:0px;
  position:absolute;
  left:1101px;
  top:965px;
  width:20px;
  height:129px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2586 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2586_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2587_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:126px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2587 {
  border-width:0px;
  position:absolute;
  left:1006px;
  top:968px;
  width:20px;
  height:126px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2587 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2587_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2588_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:113px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2588 {
  border-width:0px;
  position:absolute;
  left:1027px;
  top:981px;
  width:20px;
  height:113px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2588 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2588_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2589_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:145px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2589 {
  border-width:0px;
  position:absolute;
  left:933px;
  top:949px;
  width:20px;
  height:145px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2589 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2589_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2590_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2590 {
  border-width:0px;
  position:absolute;
  left:954px;
  top:974px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2590 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2590_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2591_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:580px;
  height:310px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2591 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:1170px;
  width:580px;
  height:310px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2591 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2591_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2592_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2592 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:1180px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2592 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2592_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2593_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2593 {
  border-width:0px;
  position:absolute;
  left:443px;
  top:1459px;
  width:10px;
  height:10px;
  display:flex;
}
#u2593 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2593_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2594_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2594 {
  border-width:0px;
  position:absolute;
  left:458px;
  top:1458px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2594 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2594_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2595_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u2595 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:1459px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u2595 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2595_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2596_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2596 {
  border-width:0px;
  position:absolute;
  left:527px;
  top:1458px;
  width:60px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2596 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2596_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2597 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2598_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2598 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:1414px;
  width:500px;
  height:1px;
  display:flex;
}
#u2598 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2598_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2599_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2599 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:1348px;
  width:500px;
  height:1px;
  display:flex;
}
#u2599 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2599_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2600_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2600 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:1315px;
  width:500px;
  height:1px;
  display:flex;
}
#u2600 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2600_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2601_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2601 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:1282px;
  width:500px;
  height:1px;
  display:flex;
}
#u2601 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2601_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2602_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2602 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:1248px;
  width:500px;
  height:1px;
  display:flex;
}
#u2602 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2602_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2603_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2603 {
  border-width:0px;
  position:absolute;
  left:248px;
  top:1405px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2603 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2603_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2604_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2604 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:1306px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2604 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2604_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2605_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2605 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:1239px;
  width:20px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2605 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2605_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2606_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2606 {
  border-width:0px;
  position:absolute;
  left:290px;
  top:1415px;
  width:1px;
  height:5px;
  display:flex;
}
#u2606 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2606_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2607_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2607 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:1425px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2607 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2607_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2608_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2608 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:1339px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2608 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2608_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2609_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2609 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:1273px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2609 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2609_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2610_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u2610 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:1381px;
  width:500px;
  height:1px;
  display:flex;
}
#u2610 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2610_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2611_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2611 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:1372px;
  width:14px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2611 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2611_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2612_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2612 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:1415px;
  width:1px;
  height:5px;
  display:flex;
}
#u2612 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2612_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2613_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2613 {
  border-width:0px;
  position:absolute;
  left:338px;
  top:1425px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2613 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2613_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2614_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2614 {
  border-width:0px;
  position:absolute;
  left:436px;
  top:1415px;
  width:1px;
  height:5px;
  display:flex;
}
#u2614 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2614_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2615_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2615 {
  border-width:0px;
  position:absolute;
  left:411px;
  top:1425px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2615 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2615_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2616_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2616 {
  border-width:0px;
  position:absolute;
  left:510px;
  top:1415px;
  width:1px;
  height:5px;
  display:flex;
}
#u2616 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2616_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2617_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2617 {
  border-width:0px;
  position:absolute;
  left:485px;
  top:1425px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2617 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2617_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2618_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2618 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:1209px;
  width:45px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2618 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2618_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2619_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2619 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:1405px;
  width:16px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2619 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2619_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2620_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2620 {
  border-width:0px;
  position:absolute;
  left:583px;
  top:1415px;
  width:1px;
  height:5px;
  display:flex;
}
#u2620 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2620_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2621_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2621 {
  border-width:0px;
  position:absolute;
  left:558px;
  top:1425px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2621 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2621_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2622_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2622 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:1306px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2622 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2622_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2623_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2623 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:1239px;
  width:30px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2623 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2623_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2624_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2624 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:1339px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2624 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2624_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2625_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2625 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:1273px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2625 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2625_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2626_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2626 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:1372px;
  width:23px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  line-height:20px;
}
#u2626 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2626_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2627_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2627 {
  border-width:0px;
  position:absolute;
  left:735px;
  top:1209px;
  width:60px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u2627 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2627_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2628_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2628 {
  border-width:0px;
  position:absolute;
  left:656px;
  top:1415px;
  width:1px;
  height:5px;
  display:flex;
}
#u2628 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2628_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2629_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2629 {
  border-width:0px;
  position:absolute;
  left:631px;
  top:1425px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2629 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2629_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2630_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u2630 {
  border-width:0px;
  position:absolute;
  left:729px;
  top:1415px;
  width:1px;
  height:5px;
  display:flex;
}
#u2630 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2630_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2631_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2631 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:1425px;
  width:51px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u2631 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2631_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2632_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:126px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2632 {
  border-width:0px;
  position:absolute;
  left:709px;
  top:1288px;
  width:20px;
  height:126px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2632 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2632_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2633_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:141px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2633 {
  border-width:0px;
  position:absolute;
  left:730px;
  top:1273px;
  width:20px;
  height:141px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2633 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2633_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2634_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:107px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2634 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:1307px;
  width:20px;
  height:107px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2634 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2634_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2635_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2635 {
  border-width:0px;
  position:absolute;
  left:291px;
  top:1294px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2635 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2635_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2636_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:107px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2636 {
  border-width:0px;
  position:absolute;
  left:636px;
  top:1307px;
  width:20px;
  height:107px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2636 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2636_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2637_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:113px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2637 {
  border-width:0px;
  position:absolute;
  left:657px;
  top:1301px;
  width:20px;
  height:113px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2637 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2637_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2638_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:118px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2638 {
  border-width:0px;
  position:absolute;
  left:563px;
  top:1296px;
  width:20px;
  height:118px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2638 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2638_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2639_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:95px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2639 {
  border-width:0px;
  position:absolute;
  left:584px;
  top:1319px;
  width:20px;
  height:95px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2639 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2639_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2640_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2640 {
  border-width:0px;
  position:absolute;
  left:490px;
  top:1294px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2640 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2640_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2641_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:129px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2641 {
  border-width:0px;
  position:absolute;
  left:511px;
  top:1285px;
  width:20px;
  height:129px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2641 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2641_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2642_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:126px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2642 {
  border-width:0px;
  position:absolute;
  left:416px;
  top:1288px;
  width:20px;
  height:126px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2642 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2642_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2643_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:113px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2643 {
  border-width:0px;
  position:absolute;
  left:437px;
  top:1301px;
  width:20px;
  height:113px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2643 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2643_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2644_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:145px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2644 {
  border-width:0px;
  position:absolute;
  left:343px;
  top:1269px;
  width:20px;
  height:145px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2644 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2644_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2645_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:120px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2645 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:1294px;
  width:20px;
  height:120px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u2645 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2645_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2646_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:482px;
  height:1345px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2646 {
  border-width:0px;
  position:absolute;
  left:1420px;
  top:150px;
  width:482px;
  height:1345px;
  display:flex;
  opacity:0.9;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2646 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2646_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2647_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:176px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2647 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:165px;
  width:176px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2647 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2647_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2648_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:5px;
  height:23px;
}
#u2648 {
  border-width:0px;
  position:absolute;
  left:1420px;
  top:163px;
  width:2px;
  height:20px;
  display:flex;
}
#u2648 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2648_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2649 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2650_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:40px;
}
#u2650 {
  border-width:0px;
  position:absolute;
  left:1704px;
  top:160px;
  width:198px;
  height:40px;
  display:flex;
}
#u2650 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2650_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2651_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u2651 {
  border-width:0px;
  position:absolute;
  left:1719px;
  top:173px;
  width:112px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u2651 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2651_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2652_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u2652 {
  border-width:0px;
  position:absolute;
  left:1841px;
  top:165px;
  width:30px;
  height:30px;
  display:flex;
}
#u2652 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2652_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2653_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:20px;
}
#u2653 {
  border-width:0px;
  position:absolute;
  left:1847px;
  top:170px;
  width:18px;
  height:20px;
  display:flex;
}
#u2653 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2653_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2654_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:10px;
}
#u2654 {
  border-width:0px;
  position:absolute;
  left:1881px;
  top:175px;
  width:6px;
  height:10px;
  display:flex;
}
#u2654 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2654_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2655_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#1868F1;
}
#u2655 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:218px;
  width:11px;
  height:19px;
  display:flex;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#1868F1;
}
#u2655 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2655_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2656_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#1868F1;
}
#u2656 {
  border-width:0px;
  position:absolute;
  left:1451px;
  top:218px;
  width:72px;
  height:19px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#1868F1;
}
#u2656 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2656_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2657_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:452px;
  height:77px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.047058823529411764);
  border:none;
  border-radius:5px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2657 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:247px;
  width:452px;
  height:77px;
  display:flex;
}
#u2657 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2657_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2658_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2658 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:262px;
  width:32px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2658 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2658_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2659_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:288px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2659 {
  border-width:0px;
  position:absolute;
  left:1584px;
  top:262px;
  width:288px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2659 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2659_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2660_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2660 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:293px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2660 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2660_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2661_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2661 {
  border-width:0px;
  position:absolute;
  left:1800px;
  top:293px;
  width:72px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2661 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2661_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2662_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2662 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:339px;
  width:10px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2662 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2662_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2663_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2663 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:339px;
  width:96px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2663 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2663_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2664_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:452px;
  height:77px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.047058823529411764);
  border:none;
  border-radius:5px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2664 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:367px;
  width:452px;
  height:77px;
  display:flex;
}
#u2664 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2664_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2665_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2665 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:382px;
  width:32px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2665 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2665_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2666_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:288px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2666 {
  border-width:0px;
  position:absolute;
  left:1584px;
  top:382px;
  width:288px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2666 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2666_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2667_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2667 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:413px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2667 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2667_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2668_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2668 {
  border-width:0px;
  position:absolute;
  left:1800px;
  top:413px;
  width:72px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2668 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2668_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2669_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2669 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:459px;
  width:10px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2669 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2669_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2670_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2670 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:459px;
  width:80px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2670 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2670_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2671_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:452px;
  height:77px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.047058823529411764);
  border:none;
  border-radius:5px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2671 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:487px;
  width:452px;
  height:77px;
  display:flex;
}
#u2671 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2671_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2672_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2672 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:502px;
  width:32px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2672 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2672_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2673_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:176px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2673 {
  border-width:0px;
  position:absolute;
  left:1696px;
  top:502px;
  width:176px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2673 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2673_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2674_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2674 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:533px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2674 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2674_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2675_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2675 {
  border-width:0px;
  position:absolute;
  left:1800px;
  top:533px;
  width:72px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2675 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2675_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2676_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2676 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:579px;
  width:10px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2676 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2676_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2677_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2677 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:579px;
  width:48px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2677 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2677_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2678_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:452px;
  height:77px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.047058823529411764);
  border:none;
  border-radius:5px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2678 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:607px;
  width:452px;
  height:77px;
  display:flex;
}
#u2678 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2678_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2679_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2679 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:622px;
  width:32px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2679 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2679_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2680_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:208px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2680 {
  border-width:0px;
  position:absolute;
  left:1664px;
  top:622px;
  width:208px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2680 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2680_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2681_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2681 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:653px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2681 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2681_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2682_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2682 {
  border-width:0px;
  position:absolute;
  left:1800px;
  top:653px;
  width:72px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2682 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2682_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2683_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2683 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:699px;
  width:10px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2683 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2683_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2684_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2684 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:699px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2684 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2684_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2685_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:452px;
  height:77px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.047058823529411764);
  border:none;
  border-radius:5px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2685 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:727px;
  width:452px;
  height:77px;
  display:flex;
}
#u2685 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2685_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2686_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2686 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:742px;
  width:32px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2686 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2686_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2687_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:288px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2687 {
  border-width:0px;
  position:absolute;
  left:1584px;
  top:742px;
  width:288px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2687 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2687_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2688_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2688 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:773px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2688 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2688_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2689_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2689 {
  border-width:0px;
  position:absolute;
  left:1800px;
  top:773px;
  width:72px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2689 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2689_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2690_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2690 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:819px;
  width:10px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2690 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2690_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2691_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2691 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:819px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2691 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2691_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2692_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:452px;
  height:77px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.047058823529411764);
  border:none;
  border-radius:5px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2692 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:847px;
  width:452px;
  height:77px;
  display:flex;
}
#u2692 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2692_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2693_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2693 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:862px;
  width:32px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2693 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2693_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2694_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:288px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2694 {
  border-width:0px;
  position:absolute;
  left:1584px;
  top:862px;
  width:288px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2694 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2694_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2695_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2695 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:893px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2695 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2695_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2696_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2696 {
  border-width:0px;
  position:absolute;
  left:1808px;
  top:893px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2696 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2696_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2697_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2697 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:939px;
  width:10px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2697 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2697_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2698_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2698 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:939px;
  width:96px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2698 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2698_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2699_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:452px;
  height:77px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.047058823529411764);
  border:none;
  border-radius:5px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2699 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:967px;
  width:452px;
  height:77px;
  display:flex;
}
#u2699 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2699_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2700_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2700 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:982px;
  width:32px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2700 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2700_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2701_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:176px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2701 {
  border-width:0px;
  position:absolute;
  left:1696px;
  top:982px;
  width:176px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2701 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2701_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2702_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2702 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:1013px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2702 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2702_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2703_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2703 {
  border-width:0px;
  position:absolute;
  left:1808px;
  top:1013px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2703 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2703_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2704_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2704 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:1059px;
  width:10px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2704 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2704_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2705_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2705 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:1059px;
  width:128px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2705 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2705_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2706_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:452px;
  height:77px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.047058823529411764);
  border:none;
  border-radius:5px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2706 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:1087px;
  width:452px;
  height:77px;
  display:flex;
}
#u2706 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2706_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2707_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2707 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:1102px;
  width:32px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2707 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2707_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2708_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:208px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2708 {
  border-width:0px;
  position:absolute;
  left:1664px;
  top:1102px;
  width:208px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2708 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2708_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2709_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2709 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:1133px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2709 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2709_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2710_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2710 {
  border-width:0px;
  position:absolute;
  left:1831px;
  top:1133px;
  width:41px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2710 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2710_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2711_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2711 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:1179px;
  width:10px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2711 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2711_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2712_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2712 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:1179px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2712 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2712_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2713_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:452px;
  height:77px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.047058823529411764);
  border:none;
  border-radius:5px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2713 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:1207px;
  width:452px;
  height:77px;
  display:flex;
}
#u2713 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2713_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2714_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2714 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:1222px;
  width:32px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2714 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2714_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2715_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2715 {
  border-width:0px;
  position:absolute;
  left:1776px;
  top:1222px;
  width:96px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2715 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2715_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2716_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2716 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:1253px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2716 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2716_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2717_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2717 {
  border-width:0px;
  position:absolute;
  left:1831px;
  top:1253px;
  width:41px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2717 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2717_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2718_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2718 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:1299px;
  width:19px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2718 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2718_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2719_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2719 {
  border-width:0px;
  position:absolute;
  left:1459px;
  top:1299px;
  width:96px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2719 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2719_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2720_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:452px;
  height:77px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.047058823529411764);
  border:none;
  border-radius:5px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2720 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:1327px;
  width:452px;
  height:77px;
  display:flex;
}
#u2720 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2720_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2721_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2721 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:1342px;
  width:32px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2721 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2721_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2722_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:176px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2722 {
  border-width:0px;
  position:absolute;
  left:1696px;
  top:1342px;
  width:176px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2722 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2722_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2723_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2723 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:1373px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u2723 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2723_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2724_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2724 {
  border-width:0px;
  position:absolute;
  left:1831px;
  top:1373px;
  width:41px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u2724 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2724_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
