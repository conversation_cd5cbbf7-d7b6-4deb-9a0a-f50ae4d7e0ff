﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="141px" height="79px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="300px" y="327px" width="141px" height="79px" filterUnits="userSpaceOnUse" id="filter15">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.2 0  " in="shadowComposite" />
    </filter>
    <g id="widget16">
      <path d="M 310 341  A 4 4 0 0 1 314 337 L 427 337  A 4 4 0 0 1 431 341 L 431 392  A 4 4 0 0 1 427 396 L 314 396  A 4 4 0 0 1 310 392 L 310 341  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" fill-opacity="0.8" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -300 -327 )">
    <use xlink:href="#widget16" filter="url(#filter15)" />
    <use xlink:href="#widget16" />
  </g>
</svg>